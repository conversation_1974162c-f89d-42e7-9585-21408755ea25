<?php
/**
 * Subscription Features Functions
 *
 * This file contains functions related to subscription plan features.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';
require_once __DIR__ . '/../language_config.php';

/**
 * Get features for a specific subscription level
 *
 * @param string $subscriptionLevel The subscription level (free, basic, advance, premium)
 * @param string $language The language code (default: 'en')
 * @return array Array of features for the subscription level
 */
function getSubscriptionFeatures($subscriptionLevel, $language = 'en') {
    global $link, $supportedLanguages;

    try {
        // Validate language
        if (!in_array(strtolower($language), $supportedLanguages)) {
            $language = 'en'; // Default to English if language not supported
        }

        // Convert language to uppercase for database column names
        $langCode = strtoupper($language);

        // Prepare the query to get features for the subscription level
        $query = "SELECT 
                    feature_key,
                    display_order,
                    title_{$langCode} as title,
                    description_{$langCode} as description
                  FROM subscription_features 
                  WHERE subscription_level = ? 
                  ORDER BY display_order ASC, feature_key ASC";

        $stmt = mysqli_prepare($link, $query);
        
        if (!$stmt) {
            throw new Exception("Database prepare error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "s", $subscriptionLevel);
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Database execute error: " . mysqli_error($link));
        }

        $result = mysqli_stmt_get_result($stmt);
        $features = [];

        while ($row = mysqli_fetch_assoc($result)) {
            // If the title or description is null for the requested language, 
            // fall back to English
            if (empty($row['title']) || empty($row['description'])) {
                $fallbackQuery = "SELECT 
                                    title_EN as title,
                                    description_EN as description
                                  FROM subscription_features 
                                  WHERE subscription_level = ? AND feature_key = ?";
                
                $fallbackStmt = mysqli_prepare($link, $fallbackQuery);
                mysqli_stmt_bind_param($fallbackStmt, "ss", $subscriptionLevel, $row['feature_key']);
                mysqli_stmt_execute($fallbackStmt);
                $fallbackResult = mysqli_stmt_get_result($fallbackStmt);
                $fallbackRow = mysqli_fetch_assoc($fallbackResult);
                
                if ($fallbackRow) {
                    $row['title'] = $row['title'] ?: $fallbackRow['title'];
                    $row['description'] = $row['description'] ?: $fallbackRow['description'];
                }
                
                mysqli_stmt_close($fallbackStmt);
            }

            $features[] = [
                'key' => $row['feature_key'],
                'title' => $row['title'],
                'description' => $row['description'],
                'order' => (int)$row['display_order']
            ];
        }

        mysqli_stmt_close($stmt);

        StripeLogger::log(StripeLogLevel::DEBUG, "Retrieved features for subscription level: $subscriptionLevel", [
            'language' => $language,
            'feature_count' => count($features)
        ]);

        return $features;

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "GET SUBSCRIPTION FEATURES ERROR: " . $e->getMessage(), [
            'subscription_level' => $subscriptionLevel,
            'language' => $language,
            'exception' => get_class($e)
        ]);
        
        // Return empty array on error
        return [];
    }
}

/**
 * Get features for all subscription levels
 *
 * @param string $language The language code (default: 'en')
 * @return array Array of features grouped by subscription level
 */
function getAllSubscriptionFeatures($language = 'en') {
    $subscriptionLevels = ['free', 'basic', 'advance', 'premium'];
    $allFeatures = [];

    foreach ($subscriptionLevels as $level) {
        $allFeatures[$level] = getSubscriptionFeatures($level, $language);
    }

    return $allFeatures;
}

/**
 * Add or update a subscription feature
 *
 * @param string $subscriptionLevel The subscription level
 * @param string $featureKey The feature key
 * @param array $titles Associative array of language codes and titles
 * @param array $descriptions Associative array of language codes and descriptions
 * @param int $displayOrder Display order (default: 0)
 * @return bool True on success, false on failure
 */
function addOrUpdateSubscriptionFeature($subscriptionLevel, $featureKey, $titles, $descriptions, $displayOrder = 0) {
    global $link, $supportedLanguages;

    try {
        // Build the query dynamically based on provided languages
        $titleColumns = [];
        $descriptionColumns = [];
        $titleValues = [];
        $descriptionValues = [];
        $params = [$subscriptionLevel, $featureKey, $displayOrder];

        foreach ($supportedLanguages as $lang) {
            $langCode = strtoupper($lang);
            $titleColumns[] = "title_{$langCode} = ?";
            $descriptionColumns[] = "description_{$langCode} = ?";
            
            $titleValues[] = isset($titles[$lang]) ? $titles[$lang] : null;
            $descriptionValues[] = isset($descriptions[$lang]) ? $descriptions[$lang] : null;
            
            $params[] = $titleValues[count($titleValues) - 1];
            $params[] = $descriptionValues[count($descriptionValues) - 1];
        }

        $allColumns = array_merge($titleColumns, $descriptionColumns);
        $setClause = implode(', ', $allColumns);

        $query = "INSERT INTO subscription_features (
                    subscription_level, feature_key, display_order, " . 
                    implode(', ', array_map(function($lang) { 
                        $langCode = strtoupper($lang); 
                        return "title_{$langCode}, description_{$langCode}"; 
                    }, $supportedLanguages)) . "
                  ) VALUES (
                    ?, ?, ?, " . str_repeat('?, ', count($supportedLanguages) * 2 - 1) . "?
                  ) ON DUPLICATE KEY UPDATE
                    display_order = VALUES(display_order), {$setClause}";

        $stmt = mysqli_prepare($link, $query);
        
        if (!$stmt) {
            throw new Exception("Database prepare error: " . mysqli_error($link));
        }

        // Create type string for bind_param
        $types = 'ssi' . str_repeat('s', count($supportedLanguages) * 2);
        
        mysqli_stmt_bind_param($stmt, $types, ...$params);
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Database execute error: " . mysqli_error($link));
        }

        mysqli_stmt_close($stmt);

        StripeLogger::log(StripeLogLevel::INFO, "Added/updated subscription feature", [
            'subscription_level' => $subscriptionLevel,
            'feature_key' => $featureKey,
            'display_order' => $displayOrder
        ]);

        return true;

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "ADD/UPDATE SUBSCRIPTION FEATURE ERROR: " . $e->getMessage(), [
            'subscription_level' => $subscriptionLevel,
            'feature_key' => $featureKey,
            'exception' => get_class($e)
        ]);
        
        return false;
    }
}

/**
 * Delete a subscription feature
 *
 * @param string $subscriptionLevel The subscription level
 * @param string $featureKey The feature key
 * @return bool True on success, false on failure
 */
function deleteSubscriptionFeature($subscriptionLevel, $featureKey) {
    global $link;

    try {
        $query = "DELETE FROM subscription_features WHERE subscription_level = ? AND feature_key = ?";
        $stmt = mysqli_prepare($link, $query);
        
        if (!$stmt) {
            throw new Exception("Database prepare error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($stmt, "ss", $subscriptionLevel, $featureKey);
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception("Database execute error: " . mysqli_error($link));
        }

        $affectedRows = mysqli_stmt_affected_rows($stmt);
        mysqli_stmt_close($stmt);

        StripeLogger::log(StripeLogLevel::INFO, "Deleted subscription feature", [
            'subscription_level' => $subscriptionLevel,
            'feature_key' => $featureKey,
            'affected_rows' => $affectedRows
        ]);

        return $affectedRows > 0;

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "DELETE SUBSCRIPTION FEATURE ERROR: " . $e->getMessage(), [
            'subscription_level' => $subscriptionLevel,
            'feature_key' => $featureKey,
            'exception' => get_class($e)
        ]);
        
        return false;
    }
}
