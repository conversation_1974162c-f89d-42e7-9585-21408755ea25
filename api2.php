<?php


cors();
$is_debug = true;

//For security purposes, we can allow only specified agent type
$user_agent = $_SERVER['HTTP_USER_AGENT'];
if (preg_match('/python/i', $user_agent)) {


    echo 'You are forbidden!';

    foreach ($_POST as $key => $value) {
        echo "a";
        error_log("Python Script Istegi : " . $key . "=" . $value, 0);
    }

    header('HTTP/1.0 403 Forbidden');

    return;
}




//$uri = $_SERVER['REQUEST_URI'];   // /api/users
//$method = $_SERVER['REQUEST_METHOD'];  // GET,POST,DELETE, etc.






include_once('config.php');


$payload = file_get_contents('php://input');


$data = json_decode($payload, true);

if ($data == null) {

    $debug_info = "";
    if ($is_debug)
        $debug_info = "Payload is not a valid json or null";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    error_log("Payload invalid = " . $payload, 0);
    return;
}



$functName = "";

if (isset($data['f'])) {
    $functName = $data['f'];
} else {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Endpoint variable is not set";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
    return;
}




$validEndpoints = array(
    "get_cr_coins",
    "get_cr_single_coin_by_id",
    "get_cr_vesting",
    "get_cr_funds",
    "get_cr_category",
    "get_cr_tags",
    "get_cr_publicsales",
    "get_cr_publicsales_extra",
    "calculate_scores",
    "calculate_ico_scores",
    "get_certik_security_scores",
    "certik_matcher",
    "get_lunarcrush_scores",
    "get_twitter_scores",
    "get_twitter_followers",
    "get_twitter_best_followers",
    "get_sparkline_history",
    "recalculate_total_score"

   
);


if (in_array($functName, $validEndpoints)) {

    if ($functName == 'get_cr_coins') {
        $functName();
    } else  if ($functName == 'get_cr_single_coin_by_id') {
        $functName();
    } 
    else  if ($functName == 'get_cr_vesting') {
        $functName();
    } 
    else  if ($functName == 'get_cr_funds') {
        $functName();
    }
    else  if ($functName == 'get_cr_category') {
        $functName();
    } 
    else  if ($functName == 'get_cr_tags') {
        $functName();
    } 
    else  if ($functName == 'get_cr_publicsales') {
        $functName();
    } 
    else  if ($functName == 'get_cr_publicsales_extra') {
        $functName();
    } 
    else  if ($functName == 'calculate_scores') {
        $functName();
    } 
    else  if ($functName == 'calculate_ico_scores') {
        $functName();
    } 
    if ($functName == 'get_certik_security_scores') {
        $functName();
    } else  if ($functName == 'certik_matcher') {
        $functName();
    } else  if ($functName == 'get_lunarcrush_scores') {
        $functName();
    }
    else  if ($functName == 'get_twitter_scores') {
        $functName();
    }
    else  if ($functName == 'get_twitter_followers') {
        $functName();
    }
    else  if ($functName == 'get_twitter_best_followers') {
        $functName();
    }
    else  if ($functName == 'get_sparkline_history') {
        $functName();
    }
    else  if ($functName == 'recalculate_total_score') {
        $functName();
    }
    
    

    
} else {
    $debug_info = "";
    if ($is_debug)
        $debug_info = "Endpoint Not Exists";



    http_response_code(404);
    echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
}




//---------------------------------------------------------------------//
//Admin Endpoints                                                      //
//---------------------------------------------------------------------//

function get_cr_coins()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $data, $link, $cr_api_key;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_coins'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_coins', now() )");
    (mysqli_stmt_execute($call));





    //First get our platform data to compare if data needs to be updated or insert

    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid from coindata");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);


    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));





    //now get live data from CoinGecko

    $success = true;
    //$parameters = $data["parameters"];


    $curl = curl_init();

    $skip = 0;
    $limit = 100;

    $is_limit_reached = false;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;
    for ($k = 0; $k < 30; $k++) {


        if ($is_limit_reached) {
            break;
        }


        $skip = $k * $limit;



        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies?include=sparkline7d&sortBy=marketCap&sortDirection=DESC&limit=' . $limit . '&skip=' . $skip,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'X-Api-Key: ' . $cr_api_key,
            ),
        ));




        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpcode != 200) {

            error_log("Response = " . $response, 0);
            return;
        }



        $cr_resp = json_decode($response, true);
        $cr_coins = $cr_resp['data'];




  

        for ($i = 0; $i < sizeof($cr_coins); $i++) {

            $progressed_coin++;


            $cr_id = $cr_coins[$i]['id'];
            $gecko_id = $cr_coins[$i]['key'];
            $symbol = $cr_coins[$i]['symbol'];
            $name = $cr_coins[$i]['name'];
            $image = $cr_coins[$i]['images']['x150'];
            $makretcap = $cr_coins[$i]['marketCap'];
            $makretcap_rank = $cr_coins[$i]['rank'];
            $fdv = $cr_coins[$i]['fullyDilutedValuation'];
            $total_volume = $cr_coins[$i]['volume24h'];
            $total_supply = $cr_coins[$i]['totalSupply'];
            $max_supply = $cr_coins[$i]['maxSupply'];
            $circulating_supply = $cr_coins[$i]['circulatingSupply'];
            $ath = $cr_coins[$i]['ath']['value'];
            $ath_change = $cr_coins[$i]['ath']['percentChange'];
            $atl = $cr_coins[$i]['atl']['value'];
            $atl_change = $cr_coins[$i]['atl']['percentChange'];
            $category_id = $cr_coins[$i]['categoryId'];
            $sparkline7d = $cr_coins[$i]['sparkline7d'];

   

            $image = isset($image) ? $image : NULL;
            $makretcap = isset($makretcap) ? $makretcap : NULL;
            $makretcap_rank = isset($makretcap_rank) ? $makretcap_rank : NULL;
            $fdv = isset($fdv) ? $fdv : NULL;
            $total_volume = isset($total_volume) ? $total_volume : NULL;
            $total_supply = isset($total_supply) ? $total_supply : NULL;
            $max_supply = !is_null($max_supply) ? $max_supply : NULL;
            $circulating_supply = isset($circulating_supply) ? $circulating_supply : NULL;
            $ath = isset($ath) ? $ath : NULL;
            $category_id = isset($category_id) ? $category_id : NULL;
            $sparkline7d = isset($sparkline7d) ? $sparkline7d : NULL;
            $ath_change = isset($ath_change) ? round($ath_change,2) : NULL;
            $atl = isset($atl) ? $atl : NULL;
            $atl_change = isset($atl_change) ? round($atl_change,2) : NULL;




            if ($makretcap != NULL && $makretcap > 0 && $makretcap < 1000000) {
                $is_limit_reached = true;
                break;
            }

            /*
        error_log("gecko_id=".$gecko_id,0);
        error_log("symbol=".$symbol,0);
        error_log("name=".$name,0);
        error_log("image=".$image,0);
        error_log("marketcap=".$makretcap,0);
        error_log("marketcap_rank=".$makretcap_rank,0);
        error_log("fdv=".$fdv,0);
        error_log("total_volume=".$total_volume,0);
        error_log("total_supply=".$total_supply,0);
        error_log("max_supply=".$max_supply,0);
        error_log("circulating_supply=".$circulating_supply,0);
        error_log("ath=".$ath,0);
        */


            $is_existing_coin = false;
            for ($s = 0; $s < sizeof($our_data); $s++) {
                if (strcmp($our_data[$s]["geckoid"], $gecko_id) == 0) {
                    $is_existing_coin = true;
                    break;
                }
            }


            if ($is_existing_coin) {

                $changed_count++;

                $call = mysqli_prepare($link, 'update coindata 
            set cr_id=?, symbol=?, name=?, image=?, marketcap=?, marketcap_rank=?, categoryid=?,
            fdv=?, total_volume=?, total_supply=?, max_supply=?, circulating_supply=?, ath=?,
            sparkline7d=?, ath_change=?, atl=?, atl_change=?,
            update_date=now() where geckoid = ?');

                mysqli_stmt_bind_param($call, 'issssiissddddsddds', $cr_id, $symbol, $name, $image, $makretcap, $makretcap_rank, $category_id, $fdv, $total_volume, $total_supply, $max_supply, $circulating_supply, $ath, $sparkline7d, $ath_change, $atl, $atl_change, $gecko_id);
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $gecko_id, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $new_count++;

                $call = mysqli_prepare($link, 'insert into coindata 
            (symbol, cr_id, geckoid, name, image, marketcap, marketcap_rank, categoryid, fdv, total_volume, total_supply, max_supply, circulating_supply, ath, sparkline7d, ath_change, atl, atl_change, create_date, update_date ) 
            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?, now(), now() )');
                mysqli_stmt_bind_param($call, 'sissssiissddddsddd', $symbol, $cr_id, $gecko_id, $name, $image, $makretcap, $makretcap_rank, $category_id, $fdv, $total_volume, $total_supply, $max_supply, $circulating_supply, $ath, $sparkline7d, $ath_change, $atl, $atl_change);
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $gecko_id, 0);
                    error_log(mysqli_error($link), 0);
                }
            }


            if (  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
                $progress = $progressed_coin /  (sizeof($our_data) / 100);
    
                $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
                (mysqli_stmt_execute($call));
            }
            
        }


       


    }




    curl_close($curl);




    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }


    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_coins'");
    (mysqli_stmt_execute($call));




    echo json_encode($resp, JSON_PRETTY_PRINT);
}






function get_cr_single_coin_by_id()
{
    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $cr_api_key, $is_debug;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_single'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_single', now() )");
    (mysqli_stmt_execute($call));



    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    //
    $rs = mysqli_query($link, "SELECT geckoid,cr_id from coindata  order by id desc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));




    $existing_funds = array();

    $call = mysqli_prepare($link, "SELECT * from cr_coin_funds");
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $existing_funds[$obj2['cr_key']][$obj2['fund_id']] = $obj2;


    }



    $existing_tags = array();

    $call = mysqli_prepare($link, "SELECT * from cr_coin_tags");
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $existing_tags[$obj2['cr_key']][$obj2['tag_id']] = $obj2;


    }


    $existing_contracts = array();

    $call = mysqli_prepare($link, "SELECT * from cr_coin_contracts");
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $existing_contracts[$obj2['cr_key']][$obj2['address']] = $obj2;


    }







    $success = true;
    $changed_count = 0;
    $new_count = 0;

    for ($i = 0; $i < sizeof($our_data); $i++) {

        $coin_id = $our_data[$i]['cr_id'];
        $geckoid = $our_data[$i]['geckoid'];



        $is_existing_coin = false;



        $call = mysqli_prepare($link, 'SELECT geckoid from coindata2 where cr_id=?');
        mysqli_stmt_bind_param($call, 'i', $coin_id);
        (mysqli_stmt_execute($call));
        $rs = mysqli_stmt_get_result($call);



        if ($rs) {
            //$row = mysqli_fetch_assoc($rs);
            $row_count = mysqli_num_rows($rs);

            //only 1 row must return for provided geckoid, no more no less.
            if ($row_count >= 1) {

                $is_existing_coin = true;
            } else {
                $is_existing_coin = false;
            }
        } else {
            $is_existing_coin = false;
        }





        $existing_categories = array();

        $call = mysqli_prepare($link, "SELECT * from coindata_categories where geckoid=?");
        mysqli_stmt_bind_param($call, 's', $coin_id);
        (mysqli_stmt_execute($call));
        $rs = mysqli_stmt_get_result($call);

        while ($obj2 = mysqli_fetch_assoc($rs)) {
            $existing_categories[] = $obj2;
        }




        $existing_exchanges = array();

        $call = mysqli_prepare($link, "SELECT * from coindata_exchanges where geckoid=?");
        mysqli_stmt_bind_param($call, 's', $coin_id);
        (mysqli_stmt_execute($call));
        $rs = mysqli_stmt_get_result($call);

        while ($obj2 = mysqli_fetch_assoc($rs)) {
            $existing_exchanges[] = $obj2;
        }





        /*
    $parameters = $data["parameters"];
    $coin_id = $parameters['coin_id'];
    error_log($coin_id,0);
    */

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/' . $coin_id . '/full-metadata',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'X-Api-Key: ' . $cr_api_key,
            ),
        ));




        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpcode != 200) {

            if (str_contains($response, '1015')) {
                echo "1015 Error - Too Many Requests Made in 1 minute so wait...";
                error_log("1015 Error - Too Many Requests Made in 1 minute so wait...", 0);
                curl_close($curl);
                die();
            }

            error_log("gecko_id=" . $coin_id, 0);
            error_log("Response = " . $response, 0);

            if (str_contains($response, 'Coin not found')) {

                $call = mysqli_prepare($link, 'delete from coindata where cr_id=?');
                mysqli_stmt_bind_param(
                    $call,
                    'i',
                    $coin_id
                );
                (mysqli_stmt_execute($call));
            }
            curl_close($curl);
            continue;
        }


        $cr_resp = json_decode($response, true);
        $cr_coins = $cr_resp['data'];



        $portfolio_user_count = 0; //şimdilik o olsun bunu ben kullanacagım bi yerde
        $description = $cr_coins['description'];


        $marketcap =  $cr_coins['marketCap'];

        $makretcap = isset($makretcap) ? $makretcap : NULL;



        if ($marketcap < 1000000 && $marketcap > 0) {




            $call = mysqli_prepare($link, 'update coindata
            set update_date=now(), isactive=0, marketcap=? where cr_id=?');
            mysqli_stmt_bind_param(
                $call,
                'ii',
                $marketcap,
                $coin_id,
            );
            $rs = (mysqli_stmt_execute($call));
        }

        $homepage = "";
        if (sizeof($cr_coins['links']) > 0)
            $links = json_encode($cr_coins['links'], true);


        $team = "";


        if (sizeof($cr_coins['team']) > 0) {
            $team =  json_encode($cr_coins['team'], true);
        }



        $price_change_1d =  $cr_coins['percentChange']['h24'];
        $price_change_7d =  $cr_coins['percentChange']['d7'];
        $price_change_30d = $cr_coins['percentChange']['d30'];
        $price_change_90d = $cr_coins['percentChange']['m3'];
        $price_change_1y =  $cr_coins['percentChange']['m6'];


        $ico_roi =  $cr_coins['icoRoi'];
        $ico_price =  $cr_coins['icoPrice'];
        $coin_age = $cr_coins['listingDate'];
        $coin_age = intval($coin_age / 1000);

        $funds =  $cr_coins['funds'];

        $tags =  $cr_coins['tagIds'];

        $contracts =  $cr_coins['contracts'];

        $hasvesting = $cr_coins['hasVesting'];
        $nexunlock = 0;
        $unlock_amount = 0;

        if(isset($hasvesting) && $hasvesting == 'true')
        {

            $nextunlock = $cr_coins['nextUnlock']['date'];
            $unlock_amount = $cr_coins['nextUnlock']['tokens'];

            if(isset($nextunlock))
            $nextunlock_Date = date('Y-m-d H:i:s', $nextunlock / 1000); // Timestamp'i datetime formatına çeviriyoruz
            else
             $nextunlock_Date = date('Y-m-d H:i:s', 0);
          



        }



                $funds =  $cr_coins['funds'];

        if(isset($funds))
        {
            $min_tier = 5;

            $fund_count = sizeof($funds);

            for($g=0; $g<sizeof($funds); $g++)
            {
                
                $min_tier = 5;
                if (isset($funds[$g]['tier']))
                {
                $last_tier = $funds[$g]['tier'];
                if($min_tier > $last_tier)
                {
                    $min_tier = $last_tier;
                }
                }
         

                
            }

            $fund_best_tier = $min_tier;


        }
        else
        {
            $fund_count = 0;
            $fund_best_tier = 5;
           
            


        }




        $portfolio_user_count = isset($portfolio_user_count) ? $portfolio_user_count : NULL;
        $description = isset($description) ? $description : NULL;
        $price_change_1d = isset($price_change_1d) ? $price_change_1d : NULL;
        $price_change_7d = isset($price_change_7d) ? $price_change_7d : NULL;
        $price_change_30d = isset($price_change_30d) ? $price_change_30d : NULL;
        $price_change_90d = isset($price_change_90d) ? $price_change_90d : NULL;
        $price_change_1y = isset($price_change_1y) ? $price_change_1y : NULL;
        $hasvesting = isset($hasvesting) ? $hasvesting : NULL;
        

        $ico_roi = isset($ico_roi) ? $ico_roi : NULL;
        $ico_price = isset($ico_price) ? $ico_price : NULL;
        $coin_age = isset($coin_age) ? $coin_age : NULL;




        if ($is_existing_coin) {

            $changed_count++;

            $call = mysqli_prepare($link, 'update coindata2
            set gecko_portfolio_count=?, description=?, links=?,
             team=?, 
             price_change_1d=round(?,2), 
             price_change_7d=round(?,2),
             price_change_30d=round(?,2), 
             price_change_90d=round(?,2), 
             price_change_1y=round(?,2),
             ico_price = ?,
             ico_roi = ?,
             coin_age = ?,
             hasvesting =?,
             nextunlock =?,
             unlock_amount=?,
             fund_count=?,
             fund_best_tier=?,

            update_date=now() where
            cr_id = ?');

            mysqli_stmt_bind_param(
                $call,
                'issssssssddiisdiii',
                $portfolio_user_count,
                $description,
                $links,
                $team,
                $price_change_1d,
                $price_change_7d,
                $price_change_30d,
                $price_change_90d,
                $price_change_1y,
                $ico_price,
                $ico_roi,
                $coin_age,
                $hasvesting,
                $nextunlock_Date,
                $unlock_amount,
                $fund_count,
                $fund_best_tier,
                $coin_id
            );
            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                curl_close($curl);
                error_log("gecko_id=" . $coin_id, 0);
                error_log(mysqli_error($link), 0);
            }
        } else {

            $new_count++;

            $call = mysqli_prepare($link, 'insert into coindata2
            (geckoid, cr_id, gecko_portfolio_count, description, links, 
            team, 
            price_change_1d, 
            price_change_7d, 
            price_change_30d, 
            price_change_90d,
            price_change_1y,
            ico_price,
            ico_roi,
            coin_age,
            hasvesting,
            nextunlock,
            unlock_amount,
            fund_count,
            fund_best_tier,

            create_date, update_date ) 
            values (?, ?, ?, ?, ?, ?, round(?,2), round(?,2), round(?,2), round(?,2), round(?,2), ?,?,?, ?,?,?,?,?,  now(), now() )');
            mysqli_stmt_bind_param(
                $call,
                'siissssssssddiisdii',
                $geckoid,
                $coin_id,
                $portfolio_user_count,
                $description,
                $links,
                $team,
                $price_change_1d,
                $price_change_7d,
                $price_change_30d,
                $price_change_90d,
                $price_change_1y,
                $ico_price,
                $ico_roi,
                $coin_age,
                $hasvesting,
                $nextunlock_Date,
                $unlock_amount,
                $fund_count,
                $fund_best_tier
            );
            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                curl_close($curl);
                error_log("gecko_id=" . $coin_id, 0);
                error_log(mysqli_error($link), 0);
            }
        }

    



        /*

        //find best ranked exchanges and listed exchange counts

        $call = mysqli_prepare($link, 'select count(*) as cex_count ,min(trust_score_rank) as best_score from gecko_exchanges where is_cex = 1 and geckoid in (select exchange_id from coindata_exchanges where geckoid = ? )');
         mysqli_stmt_bind_param($call, 's', $coin_id);
         (mysqli_stmt_execute($call));
         $rs = mysqli_stmt_get_result($call);

         $res = mysqli_fetch_assoc($rs);

         $cex_count = $res['cex_count'];
         $cex_best_rank = $res['best_score'];


         $call = mysqli_prepare($link, 'select count(*) as dex_count ,min(trust_score_rank) as best_score from gecko_exchanges where is_cex = 0 and geckoid in (select exchange_id from coindata_exchanges where geckoid = ? )');
         mysqli_stmt_bind_param($call, 's', $coin_id);
         (mysqli_stmt_execute($call));
         $rs = mysqli_stmt_get_result($call);

         $res = mysqli_fetch_assoc($rs);

         $dex_count = $res['dex_count'];
         $dex_best_rank = $res['best_score'];




         $call = mysqli_prepare($link, 'update coindata set best_cex_rank=?, best_dex_rank=?, cex_count=?, dex_count=? where geckoid=?');
         mysqli_stmt_bind_param($call, 'iiiis', $cex_best_rank, $dex_best_rank, $cex_count ,$dex_count,   $coin_id);
         (mysqli_stmt_execute($call));
         $rs = mysqli_stmt_get_result($call);


         */




         
         
         if(isset($funds))
         {
         for($r=0; $r<sizeof($funds); $r++)
         {
         
             $is_it_update = false;

         
   
             
             if ( isset($existing_funds[$geckoid][$funds[$r]['id']])  ) {
                 $is_it_update = true;
             }

             

             if ($is_it_update) {

                 $call = mysqli_prepare($link, 'update cr_coin_funds
         set cr_key=?, fund_id=?, fund_key=?, name=?, tier=?, islead=?,  update_date=now() where cr_key=? and fund_id=?');
                 mysqli_stmt_bind_param(
                     $call,
                     'sisssisi',
                     $geckoid,
                     $funds[$r]['id'],
                     $funds[$r]['key'],
                     $funds[$r]['name'],
                     $funds[$r]['tier'],
                     $funds[$r]['islead'],
                     $geckoid,
                     $funds[$r]['id']
                 );
                 $rs = (mysqli_stmt_execute($call));
 
                 if (!$rs) {
                     $success = false;
                     error_log("gecko_id2=" . $geckoid, 0);
                     error_log(mysqli_error($link), 0);
                 }
             } else {
 
                 $call = mysqli_prepare($link, 'insert into cr_coin_funds
         (fund_id, fund_key, cr_key, name, tier, islead  ) 
         values (?, ?,?,?,?,?)');
                 mysqli_stmt_bind_param(
                     $call,
                     'issssi',
                     $funds[$r]['id'],
                     $funds[$r]['key'],
                     $geckoid,
                     $funds[$r]['name'],
                     $funds[$r]['tier'],
                     $funds[$r]['islead']
                 );
                 $rs = (mysqli_stmt_execute($call));
 
                 if (!$rs) {
                     $success = false;
                     error_log("gecko_id3=" . $geckoid, 0);
                     error_log(mysqli_error($link), 0);
                 }
             }



         

          }

          

         }






         if(isset($tags))
         {
         for($r=0; $r<sizeof($tags); $r++)
         {
         
             $is_it_update = false;

         
   
             
             if ( isset($existing_tags[$geckoid][$tags[$r]])  ) {
                 $is_it_update = true;
             }

             

             if ($is_it_update) {

                 $call = mysqli_prepare($link, 'update cr_coin_tags
         set cr_key=?, cr_id=?, tag_id=?,  update_date=now() where cr_key=? and tag_id=?');
                 mysqli_stmt_bind_param(
                     $call,
                     'siisi',
                     $geckoid,
                     $coin_id,
                     $tags[$r],
                     $geckoid,
                     $tags[$r]
                 );
                 $rs = (mysqli_stmt_execute($call));
 
                 if (!$rs) {
                     $success = false;
                     error_log("gecko_id2=" . $geckoid, 0);
                     error_log(mysqli_error($link), 0);
                 }
             } else {
 
                 $call = mysqli_prepare($link, 'insert into cr_coin_tags
         (tag_id, cr_key, cr_id, create_date ) 
         values (?,?,?, now())');
                 mysqli_stmt_bind_param(
                     $call,
                     'isi',
                     $tags[$r],
                     $geckoid,
                     $coin_id

                 );
                 $rs = (mysqli_stmt_execute($call));
 
                 if (!$rs) {
                     $success = false;
                     error_log("gecko_id3=" . $geckoid, 0);
                     error_log(mysqli_error($link), 0);
                 }
             }



         

          }

         }



         if(isset($contracts))
         {
         for($r=0; $r<sizeof($contracts); $r++)
         {
         
             $is_it_update = false;

         
   
             
             if ( isset($existing_contracts[$geckoid][$contracts[$r]['address']])  ) {
                 $is_it_update = true;
             }

             

             if ($is_it_update) {

                 $call = mysqli_prepare($link, 'update cr_coin_contracts
         set cr_key=?, cr_id=?, address=?, decimals=?, platform_id=?, platform_key=?, platform_name=?,  update_date=now() where cr_key=? and address=?');
                 mysqli_stmt_bind_param(
                     $call,
                     'sisiissss',
                     $geckoid,
                     $coin_id,
                     $contracts[$r]['address'],
                     $contracts[$r]['decimals'],
                     $contracts[$r]['platform']['id'],
                     $contracts[$r]['platform']['key'],
                     $contracts[$r]['platform']['name'],
                     $geckoid,
                     $contracts[$r]['address'],
                 );
                 $rs = (mysqli_stmt_execute($call));
 
                 if (!$rs) {
                     $success = false;
                     error_log("gecko_id2=" . $geckoid, 0);
                     error_log(mysqli_error($link), 0);
                 }
             } else {
 
                 $call = mysqli_prepare($link, 'insert into cr_coin_contracts
         (address, cr_key, cr_id, decimals, platform_id, platform_key, platform_name, create_date ) 
         values (?,?,?,?,?,?,?, now())');
                 mysqli_stmt_bind_param(
                     $call,
                     'ssiiiss',
                     $contracts[$r]['address'],
                     $geckoid,
                     $coin_id,
                     $contracts[$r]['decimals'],
                     $contracts[$r]['platform']['id'],
                     $contracts[$r]['platform']['key'],
                     $contracts[$r]['platform']['name']



                 );
                 $rs = (mysqli_stmt_execute($call));
 
                 if (!$rs) {
                     $success = false;
                     error_log("gecko_id3=" . $geckoid, 0);
                     error_log(mysqli_error($link), 0);
                 }
             }



         

          }

         }


         




        if (($i + 1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $i /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }




        usleep(700000);
        //sleep(1);



        //  if($i == 10)
        //  break;
    }







    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        curl_close($curl);
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_single'");
    (mysqli_stmt_execute($call));

    echo json_encode($resp, JSON_PRETTY_PRINT);



    curl_close($curl);
}


function get_cr_vesting()
{



    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $cr_api_key, $is_debug;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_vesting'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_vesting', now() )");
    (mysqli_stmt_execute($call));



    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    //
    $rs = mysqli_query($link, "SELECT coindata.geckoid,coindata.cr_id,hasvesting from coindata left join coindata2 on
    coindata2.cr_id = coindata.cr_id  where coindata2.hasvesting = 1 order by coindata.id desc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));



    $success = true;
    $changed_count = 0;
    $new_count = 0;

    for ($i = 0; $i < sizeof($our_data); $i++) {

        $coin_id = $our_data[$i]['cr_id'];
        $geckoid = $our_data[$i]['geckoid'];



        $is_existing_coin = false;



        $call = mysqli_prepare($link, 'SELECT geckoid from cr_vesting where cr_id=?');
        mysqli_stmt_bind_param($call, 'i', $coin_id);
        (mysqli_stmt_execute($call));
        $rs = mysqli_stmt_get_result($call);



        if ($rs) {
            //$row = mysqli_fetch_assoc($rs);
            $row_count = mysqli_num_rows($rs);

            //only 1 row must return for provided geckoid, no more no less.
            if ($row_count >= 1) {

                $is_existing_coin = true;
            } else {
                $is_existing_coin = false;
            }
        } else {
            $is_existing_coin = false;
        }






        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/'.$coin_id. '/vesting',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'X-Api-Key: ' . $cr_api_key,
            ),
        ));




        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpcode != 200) {

            if (str_contains($response, '1015')) {
                echo "1015 Error - Too Many Requests Made in 1 minute so wait...";
                error_log("1015 Error - Too Many Requests Made in 1 minute so wait...", 0);
                curl_close($curl);
                die();
            }

            error_log("gecko_id=" . $coin_id, 0);
            error_log("Response = " . $response, 0);

            if (str_contains($response, 'Coin not found')) {

                /*
                $call = mysqli_prepare($link, 'delete from cr_vesting where cr_id=?');
                mysqli_stmt_bind_param(
                 
                    $call,
                    'i',
                    $coin_id
                );
                (mysqli_stmt_execute($call));

                */
            }

            usleep(500000);
            curl_close($curl);
            continue;
        }


        $cr_resp = json_decode($response, true);
        $cr_coins = $cr_resp['data'];




     
        $symbol =  $cr_coins['symbol'];
        $marketcap =  $cr_coins['marketCap'];
        $fdv = $cr_coins['fullyDilutedValuation'];
        $price =  $cr_coins['price'];
        $totalUnlocked =  $cr_coins['totalUnlocked'];
        $totalLocked =  $cr_coins['totalLocked'];
        $totalUntracked =  $cr_coins['totalUntracked'];
        $totalSupply =  $cr_coins['totalSupply'];
        $maxSupply =  $cr_coins['maxSupply'];
        $circulatingSupply =  $cr_coins['circulatingSupply'];
        
        


        $price = isset($price) ? $price : NULL;
        $marketcap = isset($marketcap) ? $marketcap : NULL;
        $fdv = isset($fdv) ? $fdv : NULL;
        $totalUnlocked = isset($totalUnlocked) ? $totalUnlocked : NULL;
        $totalLocked = isset($totalLocked) ? $totalLocked : NULL;
        $totalUntracked = isset($totalUntracked) ? $totalUntracked : NULL;
        $totalSupply = isset($totalSupply) ? $totalSupply : NULL;
        $maxSupply = isset($maxSupply) ? $maxSupply : NULL;
        $circulatingSupply = isset($circulatingSupply) ? $circulatingSupply : NULL;

    
        $tmp_total_supply = $maxSupply;
        if (empty($maxSupply) || $maxSupply == 0) {
        // Alternatif: totalSupply varsa onu kullan
        $tmp_total_supply = floatval($data['data']['totalSupply'] ?? 1);
        }

       $allocations = $cr_resp['data']['allocations'];
       $today = strtotime('2025-06-19') * 1000; // millisaniye
       $nextYear = strtotime('2026-06-19') * 1000;
       $emission_1y = calculateEmissionRatio($allocations, $tmp_total_supply, $today, $nextYear);

  




        if ($is_existing_coin) {

            $changed_count++;

            $call = mysqli_prepare($link, 'update cr_vesting
            set price=?, marketcap=?, fdv=?,
             totalUnlocked=?, 
             totalLocked=?, 
             totalUntracked=?,
             circulatingSupply=?, 
             maxSupply=?, 
             totalSupply=?,
             geckoid = ?,
             symbol = ?,
             emission_1y=?,
            update_date=now() where
            cr_id = ?');

            mysqli_stmt_bind_param(
                $call,
                'dddddddddssdi',
                $price,
                $marketcap,
                $fdv,
                $totalUnlocked,
                $totalLocked,
                $totalUntracked,
                $circulatingSupply,
                $maxSupply,
                $totalSupply,
                $geckoid,
                $symbol,
                $emission_1y,
                $coin_id
            );
            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                curl_close($curl);
                error_log("gecko_id=" . $coin_id, 0);
                error_log(mysqli_error($link), 0);
            }
        } else {

            $new_count++;

            $call = mysqli_prepare($link, 'insert into cr_vesting
            (cr_id, geckoid, symbol, price, marketcap, fdv, 
            totalUnlocked, 
            totalLocked, 
            totalUntracked, 
            circulatingSupply, 
            maxSupply,
             totalSupply,
             emission_1y,
            create_date, update_date ) 
            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,  now(), now() )');
            mysqli_stmt_bind_param(
                $call,
                'issdddddddddd',
                $coin_id,
                $geckoid,
                $symbol,
                $price,
                $marketcap,
                $fdv,
                $totalUnlocked,
                $totalLocked,
                $totalUntracked,
                $circulatingSupply,
                $maxSupply,
                $totalSupply,
                $emission_1y
            );
            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                curl_close($curl);
                error_log("gecko_id=" . $coin_id, 0);
                error_log(mysqli_error($link), 0);
            }
        }

      


        if (($i + 1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $i /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }




        usleep(500000);
        //sleep(1);



        //  if($i == 10)
        //  break;
    }







    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        curl_close($curl);
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_vesting'");
    (mysqli_stmt_execute($call));

    echo json_encode($resp, JSON_PRETTY_PRINT);



    curl_close($curl);




}

function get_cr_funds()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $cr_api_key, $is_debug;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_funds'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_funds', now() )");
    (mysqli_stmt_execute($call));





    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT cr_id from cr_funds order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }

    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));



    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.cryptorank.io/v2/funds/map',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'X-Api-Key: ' . $cr_api_key,
        ),
    ));


    $response = curl_exec($curl);

    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if ($httpcode != 200) {

        error_log("Response = " . $response, 0);
        return;
    }


    curl_close($curl);

    $cr_funds_resp = json_decode($response, true);

    $cr_funds = $cr_funds_resp['data'];

    $success = true;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;


    for ($i = 0; $i < sizeof($cr_funds); $i++) {

        $progressed_coin++;

        $cr_id = $cr_funds[$i]['id'];
        $cr_key = $cr_funds[$i]['key'];
        $name = $cr_funds[$i]['name'];
        $tier = $cr_funds[$i]['tier'];
        $type = $cr_funds[$i]['type'];
   

   



        $is_existing_coin = false;
        for ($s = 0; $s < sizeof($our_data); $s++) {
            if (strcmp($our_data[$s]["cr_id"], $cr_id) == 0) {
                $is_existing_coin = true;
                break;
            }
        }


        if ($is_existing_coin) {

            $changed_count++;


            $call = mysqli_prepare($link, 'UPDATE cr_funds 
        SET 
        name = ?, 
        cr_key = ?,
        tier = ?,
        fund_type = ? ,
        update_date = now()
    WHERE cr_id = ?');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "ssssi", // Veri tipleri
                $name,
                $cr_key,
                $tier,
                $type,
                $cr_id
            

            );


            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("cr_key=" . $cr_key, 0);
                error_log(mysqli_error($link), 0);
            }
        } else {

            $new_count++;


            $call = mysqli_prepare($link, 'INSERT INTO cr_funds 
    (cr_id, cr_key, name, tier, fund_type, create_date) 
    VALUES 
    (?, ?, ?, ?, ?,  now() )');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "issss",
                $cr_id,
                $cr_key,
                $name,
                $tier,
                $type
      
            );

            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("cr_key=" . $cr_key, 0);
                error_log(mysqli_error($link), 0);
            }
        }

     

        if (  ($progressed_coin+1)  %  (sizeof($cr_funds) / 100) == 0) {
            $progress = $progressed_coin /  (sizeof($cr_funds) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


    }


    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_funds'");
    (mysqli_stmt_execute($call));





    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function get_cr_category()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $cr_api_key, $is_debug;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_category'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_category', now() )");
    (mysqli_stmt_execute($call));





    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT cr_id from cr_category order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }

    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));



    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/categories',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'X-Api-Key: ' . $cr_api_key,
        ),
    ));


    $response = curl_exec($curl);

    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if ($httpcode != 200) {

        error_log("Response = " . $response, 0);
        return;
    }


    curl_close($curl);

    $cr_funds_resp = json_decode($response, true);

    $cr_funds = $cr_funds_resp['data'];

    $success = true;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;


    for ($i = 0; $i < sizeof($cr_funds); $i++) {

        $progressed_coin++;

        $cr_id = $cr_funds[$i]['id'];
        $cr_key = $cr_funds[$i]['key'];
        $name = $cr_funds[$i]['name'];
        $project_count = $cr_funds[$i]['numberOfProjects'];
      
   

   



        $is_existing_coin = false;
        for ($s = 0; $s < sizeof($our_data); $s++) {
            if (strcmp($our_data[$s]["cr_id"], $cr_id) == 0) {
                $is_existing_coin = true;
                break;
            }
        }


        if ($is_existing_coin) {

            $changed_count++;


            $call = mysqli_prepare($link, 'UPDATE cr_category
        SET 
        cr_name = ?, 
        cr_key = ?,
        projects = ? ,
        update_date = now()
    WHERE cr_id = ?');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "ssii", // Veri tipleri
                $name,
                $cr_key,
                $project_count,
                $cr_id
            

            );


            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("cr_key=" . $cr_key, 0);
                error_log(mysqli_error($link), 0);
            }
        } else {

            $new_count++;


            $call = mysqli_prepare($link, 'INSERT INTO cr_category
    (cr_id, cr_key, cr_name, projects, create_date) 
    VALUES 
    (?, ?, ?, ?,  now() )');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "issi",
                $cr_id,
                $cr_key,
                $name,
                $project_count
      
            );

            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("cr_key=" . $cr_key, 0);
                error_log(mysqli_error($link), 0);
            }
        }

     

        if (  ($progressed_coin+1)  %  (sizeof($cr_funds) ) == 0) {
            $progress = $progressed_coin /  (sizeof($cr_funds) * 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


    }


    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_category'");
    (mysqli_stmt_execute($call));





    echo json_encode($resp, JSON_PRETTY_PRINT);
}




function get_cr_tags()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $cr_api_key, $is_debug;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_tags'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_tags', now() )");
    (mysqli_stmt_execute($call));





    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT cr_id from cr_tags order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }

    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));



    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/tags',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'X-Api-Key: ' . $cr_api_key,
        ),
    ));


    $response = curl_exec($curl);

    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if ($httpcode != 200) {

        error_log("Response = " . $response, 0);
        return;
    }


    curl_close($curl);

    $cr_funds_resp = json_decode($response, true);

    $cr_funds = $cr_funds_resp['data'];

    $success = true;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;


    for ($i = 0; $i < sizeof($cr_funds); $i++) {

        $progressed_coin++;

        $cr_id = $cr_funds[$i]['id'];
        $cr_key = $cr_funds[$i]['key'];
        $name = $cr_funds[$i]['name'];
        $project_count = $cr_funds[$i]['numberOfProjects'];
      
   

   



        $is_existing_coin = false;
        for ($s = 0; $s < sizeof($our_data); $s++) {
            if (strcmp($our_data[$s]["cr_id"], $cr_id) == 0) {
                $is_existing_coin = true;
                break;
            }
        }


        if ($is_existing_coin) {

            $changed_count++;


            $call = mysqli_prepare($link, 'UPDATE cr_tags
        SET 
        cr_name = ?, 
        cr_key = ?,
        projects = ? ,
        update_date = now()
    WHERE cr_id = ?');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "ssii", // Veri tipleri
                $name,
                $cr_key,
                $project_count,
                $cr_id
            

            );


            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("cr_key=" . $cr_key, 0);
                error_log(mysqli_error($link), 0);
            }
        } else {

            $new_count++;


            $call = mysqli_prepare($link, 'INSERT INTO cr_tags
    (cr_id, cr_key, cr_name, projects, create_date) 
    VALUES 
    (?, ?, ?, ?,  now() )');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "issi",
                $cr_id,
                $cr_key,
                $name,
                $project_count
      
            );

            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("cr_key=" . $cr_key, 0);
                error_log(mysqli_error($link), 0);
            }
        }

     

        if (  ($progressed_coin+1)  %  (sizeof($cr_funds) ) / 100  == 0) {
            $progress = $progressed_coin /  (sizeof($cr_funds) / 100  );

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


    }


    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_tags'");
    (mysqli_stmt_execute($call));





    echo json_encode($resp, JSON_PRETTY_PRINT);
}



function get_cr_publicsales()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $data, $link, $cr_api_key;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_publicsales'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_publicsales', now() )");
    (mysqli_stmt_execute($call));





    $existing_launchpads = array();

    $call = mysqli_prepare($link, "SELECT cr_key from cr_publicsales_launchpads");
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);

    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $existing_launchpads[$obj2['cr_key']] = $obj2;


    }


    


    $lp_quality = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select lp_id, name, tier from cr_launchpads");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        $lp_quality[$obj2['lp_id']] = $obj2;
       
    }




    $lp_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from cr_publicsales_launchpads");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        if( isset($lp_quality[$obj2['lp_id']]) )
        {
            $obj2['tier'] = $lp_quality[$obj2['lp_id']]['tier'];
        }
        else
        {
            $obj2['tier'] = 5;
        }
       

        $lp_data[$obj2['cr_key']][] = $obj2;
       
    }




    /*
    $call = mysqli_prepare($link, 'delete from cr_publicsales');
    (mysqli_stmt_execute($call));
    */



    //First get our platform data to compare if data needs to be updated or insert

    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT cr_key from cr_publicsales");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



    $our_data_count = sizeof($our_data);


    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));





    //now get live data from CoinGecko

    $success = true;
    //$parameters = $data["parameters"];


    $curl = curl_init();

    $skip = 0;
    $limit = 300;

    $is_limit_reached = false;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;
    for ($k = 0; $k < 200; $k++) {


        if ($is_limit_reached) {
            break;
        }


        $skip = $k * $limit;



        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/public-sales?sortBy=startDate&sortDirection=DESC&&limit=' . $limit . '&skip=' . $skip,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'X-Api-Key: ' . $cr_api_key,
            ),
        ));




        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpcode != 200) {


            error_log("Response = " . $response, 0);
            return;
        }



        $cr_resp = json_decode($response, true);
        $cr_coins = $cr_resp['data'];

        if(sizeof($cr_coins) == 0)
        {
            //tum coinler gelmiş demektir bitirelim, cunku sayfa sayısı gelmiyor ancak boyle anlarız.
            
            $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        
            $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_publicsales'");
            (mysqli_stmt_execute($call));
            

        }



  

        for ($i = 0; $i < sizeof($cr_coins); $i++) {

            $progressed_coin++;


            $cr_id = $cr_coins[$i]['id'];
            $cr_key = $cr_coins[$i]['key'];
            $symbol = $cr_coins[$i]['symbol'];
            $name = $cr_coins[$i]['name'];
            $type = $cr_coins[$i]['type'];
            $image = $cr_coins[$i]['images']['x150'];
            $makretcap = $cr_coins[$i]['marketCap'];
            $makretcap_rank = $cr_coins[$i]['rank'];
            $fdv = $cr_coins[$i]['fullyDilutedValuation'];
            $total_supply = $cr_coins[$i]['totalSupply'];
            $circulating_supply = $cr_coins[$i]['circulatingSupply'];
            $roi = $cr_coins[$i]['crowdsale']['roi']; 
            $ath_roi = $cr_coins[$i]['crowdsale']['athRoi']; 
            $initial_marketcap = $cr_coins[$i]['initialMarketCap'];
            $launchpads = $cr_coins[$i]['crowdsale']['launchpad'];
            $ido_type = $cr_coins[$i]['crowdsale']['type'];
            $startDate = $cr_coins[$i]['crowdsale']['startDate']; 
            $endDate = $cr_coins[$i]['crowdsale']['endDate']; 
            $tokensForSale = $cr_coins[$i]['crowdsale']['tokensForSale']; 
            $price = $cr_coins[$i]['crowdsale']['price']; 
            $raise = $cr_coins[$i]['crowdsale']['raise']; 
            $allocationOfSupply = $cr_coins[$i]['crowdsale']['allocationOfSupply']; 
            $crowdsale_status = $cr_coins[$i]['crowdsale']['status']; 
            $category_id = $cr_coins[$i]['categoryId'];
            $tagids = $cr_coins[$i]['tagIds'];

            //aslında farklı bi public sale oldugun buradna anlayacagız ancak null gelenler var onlara da biz 0 verelim.
            $cr_lp_id = 0;
            if(isset( $cr_coins[$i]['crowdsale']['launchpad']))
            {
                $cr_lp_id = $cr_coins[$i]['crowdsale']['launchpad']['id'];
            }
            

            if(isset($lp_data[$cr_key]))
            $lp_count = sizeof($lp_data[$cr_key]) ;
            else
            $lp_count = 0;
    
            $lp_tier = 5;

            if(!isset($lp_data[$cr_key]))
            $lp_data_count = 0;
            else
            $lp_data_count = sizeof($lp_data[$cr_key]);

            for($h=0; $h < $lp_data_count; $h++)
            {
                $min_tier = 5;
                if (isset($lp_quality[$lp_data[$cr_key][$h]['lp_id']]['tier']))
                {
                $last_tier = $lp_quality[$lp_data[$cr_key][$h]['lp_id']]['tier'];
                if($min_tier > $last_tier)
                {
                    $min_tier = $last_tier;
                }
                }


            }
            $lp_tier = $min_tier;
            

   

            $image = isset($image) ? $image : NULL;
            $makretcap = isset($makretcap) ? $makretcap : NULL;
            $makretcap_rank = isset($makretcap_rank) ? $makretcap_rank : NULL;
            $fdv = isset($fdv) ? $fdv : NULL;
            $total_supply = isset($total_supply) ? $total_supply : NULL;
            $circulating_supply = isset($circulating_supply) ? $circulating_supply : NULL;
            $roi = isset($roi) ? $roi : NULL;
            $ath_roi = isset($ath_roi) ? $ath_roi : NULL;
            $initial_marketcap = isset($initial_marketcap) ? $initial_marketcap : NULL;
            $ido_type = isset($ido_type) ? $ido_type : NULL;
   
            $startDate = isset($startDate) ? intval($startDate / 1000) : 0;
            $endDate = isset($endDate) ? intval($endDate / 1000) : 0;
            $tokensForSale = isset($tokensForSale) ? $tokensForSale : 0;
            $price = isset($price) ? $price : NULL;
            $raise = isset($raise) ? $raise : 0;
            $allocationOfSupply = isset($allocationOfSupply) ? $allocationOfSupply : 0;
            $lp_count = isset($lp_count) ? $lp_count : 0;
            $cr_lp_id = isset($cr_lp_id) ? $cr_lp_id : 0;
            $category_id = isset($category_id) ? $category_id : NULL;
            $tagids = isset($tagids) ? json_encode($tagids) : NULL;
            $type = isset($type) ? $type : NULL;

  


       

       

            /*
        error_log("gecko_id=".$gecko_id,0);
        error_log("symbol=".$symbol,0);
        error_log("name=".$name,0);
        error_log("image=".$image,0);
        error_log("marketcap=".$makretcap,0);
        error_log("marketcap_rank=".$makretcap_rank,0);
        error_log("fdv=".$fdv,0);
        error_log("total_volume=".$total_volume,0);
        error_log("total_supply=".$total_supply,0);
        error_log("max_supply=".$max_supply,0);
        error_log("circulating_supply=".$circulating_supply,0);
        error_log("ath=".$ath,0);
        */


            $is_existing_coin = false;
            /*
            for ($s = 0; $s < sizeof($our_data); $s++) {
                if (strcmp($our_data[$s]["cr_key"], $cr_key) == 0) {
                    $is_existing_coin = true;
                    break;
                }
            }
            */


            //yukarıda sildigimiz için aslında buray hiç girmeyecek yani hiç bir zaman update olmayacak.
            if ($is_existing_coin) {

                $changed_count++;

                $call = mysqli_prepare($link, 'update cr_publicsales 
            set cr_id=?, symbol=?, cr_name=?, image=?, marketcap=?, marketcap_rank=?,
            fdv=?, total_supply=?, circulating_supply=?, crowdsale_roi=?,
            crowdsale_ath_roi=?, initial_marketcap=?, crowdsale_type=?,
            update_date=now() where cr_key = ?');

                mysqli_stmt_bind_param($call, 'issssissddddss',
                 $cr_id, $symbol, $name, $image, $makretcap, $makretcap_rank,
                  $fdv, $total_supply, $circulating_supply, $roi,
                   $ath_roi, $initial_marketcap, $ido_type, $cr_key);
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $cr_key, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $new_count++;

                        $call = mysqli_prepare($link, 'INSERT INTO cr_publicsales 
                (symbol, cr_id, cr_key, cr_name, image, marketcap, marketcap_rank, fdv, total_supply, 
                circulating_supply, crowdsale_roi, crowdsale_ath_roi, initial_marketcap, crowdsale_type,
                crowdsale_startdate, crowdsale_enddate, crowdsale_tokenforsale, crowdsale_price, crowdsale_raise, crowdsale_alloc_supply,
                crowdsale_status, lp_count, lp_best_tier, category_id, tag_data, coin_type,
                create_date, update_date, lp_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?)
                ON DUPLICATE KEY UPDATE 
                symbol = VALUES(symbol),
                cr_key = VALUES(cr_key),
                cr_name = VALUES(cr_name),
                image = VALUES(image),
                marketcap = VALUES(marketcap),
                marketcap_rank = VALUES(marketcap_rank),
                fdv = VALUES(fdv),
                total_supply = VALUES(total_supply),
                circulating_supply = VALUES(circulating_supply),
                crowdsale_roi = VALUES(crowdsale_roi),
                crowdsale_ath_roi = VALUES(crowdsale_ath_roi),
                initial_marketcap = VALUES(initial_marketcap),
                crowdsale_startdate = VALUES(crowdsale_startdate),
                crowdsale_enddate = VALUES(crowdsale_enddate),
                crowdsale_tokenforsale = VALUES(crowdsale_tokenforsale),
                crowdsale_price = VALUES(crowdsale_price),
                crowdsale_raise = VALUES(crowdsale_raise),
                crowdsale_alloc_supply = VALUES(crowdsale_alloc_supply),
                crowdsale_status = VALUES(crowdsale_status),
                lp_count = VALUES(lp_count),
                lp_best_tier = VALUES(lp_best_tier),
                category_id = VALUES(category_id),
                tag_data = VALUES(tag_data),
                crowdsale_type = VALUES(crowdsale_type),
                coin_type = VALUES(coin_type),
                update_date = NOW();');

                mysqli_stmt_bind_param($call, 'sissssisssddssssididsiiissi',
                    $symbol, $cr_id, $cr_key, $name,
                    $image, $makretcap, $makretcap_rank, $fdv,
                    $total_supply, $circulating_supply, $roi, $ath_roi, $initial_marketcap, $ido_type,
                    $startDate, $endDate, $tokensForSale, $price, $raise, $allocationOfSupply,
                    $crowdsale_status, $lp_count, $lp_tier, $category_id, $tagids, $type, $cr_lp_id
                );

                $rs = mysqli_stmt_execute($call);

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $cr_key, 0);
                    error_log(mysqli_error($link), 0);
                }
            }

         

            if ( (sizeof($our_data) / 100) > 0 &&  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
                $progress = $progressed_coin /  (sizeof($our_data) / 100);
    
                $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
                (mysqli_stmt_execute($call));
            }
           




            if(isset($launchpads))
            {
         
                $is_it_update = false;

      
                
                if ( isset($existing_launchpads[$cr_key])  ) {
                    $is_it_update = true;
                }

                

                if ($is_it_update) {

                    $call = mysqli_prepare($link, 'update cr_publicsales_launchpads
            set cr_key=?, lp_id=?, name=?, image=?,  update_date=now() where cr_key=? and lp_id=?');
                    mysqli_stmt_bind_param(
                        $call,
                        'sisssi',
                        $cr_key,
                        $launchpads['id'],
                        $launchpads['name'],
                        $launchpads['image'],
                        $cr_key,
                        $launchpads['id']
                    );
                    $rs = (mysqli_stmt_execute($call));
    
                    if (!$rs) {
                        $success = false;
                        error_log("gecko_id2=" . $cr_key, 0);
                        error_log(mysqli_error($link), 0);
                    }
                } else {
    
                    $call = mysqli_prepare($link, 'insert into cr_publicsales_launchpads
            (cr_key, lp_id, name, image  ) 
            values (?, ?,?, ?)');
                    mysqli_stmt_bind_param(
                        $call,
                        'siss',
                        $cr_key,
                        $launchpads['id'],
                        $launchpads['name'],
                        $launchpads['image'],
                    );
                    $rs = (mysqli_stmt_execute($call));
    
                    if (!$rs) {
                        $success = false;
                        error_log("gecko_id3=" . $cr_key, 0);
                        error_log(mysqli_error($link), 0);
                    }
                }

             

            }

        }


       


    }




    curl_close($curl);




    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }


    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_publicsales'");
    (mysqli_stmt_execute($call));




    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function get_cr_publicsales_extra()
{

    global  $link, $cr_api_key;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='cr_publicsales_extra'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'cr_publicsales_extra', now() )");
    (mysqli_stmt_execute($call));





    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from cr_publicsales_grouped where crowdsale_status='upcoming'");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



    $our_data_count = sizeof($our_data);


    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));





    $progressed_coin = 0;
   

    for($i=0; $i<$our_data_count; $i++) {

      $progressed_coin++;
        $coin_id = $our_data[$i]['cr_id'];
        $coin_key = $our_data[$i]['cr_key'];




        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/' . $coin_id . '/full-metadata',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json',
                'X-Api-Key: ' . $cr_api_key,
            ),
        ));




        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpcode != 200) {

            if (str_contains($response, '1015')) {
                echo "1015 Error - Too Many Requests Made in 1 minute so wait...";
                error_log("1015 Error - Too Many Requests Made in 1 minute so wait...", 0);
                curl_close($curl);
                die();
            }

            error_log("gecko_id=" . $coin_id, 0);
            error_log("Response = " . $response, 0);

            if (str_contains($response, 'Coin not found')) {

                $call = mysqli_prepare($link, 'delete from coindata where cr_id=?');
                mysqli_stmt_bind_param(
                    $call,
                    'i',
                    $coin_id
                );
                (mysqli_stmt_execute($call));
            }
            curl_close($curl);
            continue;
        }

        



        $cr_resp = json_decode($response, true);
        $cr_coins = $cr_resp['data'];
        $hasvesting = $cr_coins['hasVesting'];
        $description = $cr_coins['description'];


        $team = "";
        if (sizeof($cr_coins['team']) > 0) {
            $team =  json_encode($cr_coins['team'], true);
        }


        $contracts = "";
        if (sizeof($cr_coins['contracts']) > 0) {
            $contracts =  json_encode($cr_coins['contracts'], true);
        }

        $funds =  $cr_coins['funds'];

        if(isset($funds))
        {
            $fund_count = sizeof($funds);
             $min_tier = 5;

            for($g=0; $g<sizeof($funds); $g++)
            {
                
                $min_tier = 5;
                if (isset($funds[$g]['tier']))
                {
                $last_tier = $funds[$g]['tier'];
                if($min_tier > $last_tier)
                {
                    $min_tier = $last_tier;
                }
                }
         

                
            }

            $fund_best_tier = $min_tier;


        }
        else
        {
            $fund_count = 0;
            $fund_best_tier = 6;
           
            


        }

        $hasvesting = isset($hasvesting) ? $hasvesting : NULL;


        $links = "";
        if (sizeof($cr_coins['links']) > 0)
            $links = json_encode($cr_coins['links'], true);



        
        $links = json_encode($cr_coins['links'], true);


        $call = mysqli_prepare($link, "update cr_publicsales set links = ?, hasvesting=?, fund_count=?, fund_best_tier=?, description=?, team_data=?, contract_data=?  where cr_key = ?");
            mysqli_stmt_bind_param(
                $call,
                'siiissss',
                $links,
                $hasvesting,
                $fund_count,
                $fund_best_tier,
                $description,
                $team,
                $contracts,
                $coin_key
            );
            $rs2 = (mysqli_stmt_execute($call));
        




            if ( (sizeof($our_data) / 100) > 0 &&  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
                $progress = $progressed_coin /  (sizeof($our_data) / 100);
    
                $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
                (mysqli_stmt_execute($call));
            }
            




        sleep(2);


    }


    $changed_count = 0;
    $new_count = 0;
    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='cr_publicsales_extra'");
    (mysqli_stmt_execute($call));






}


function get_twitter_scores()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global  $link,$twitter_score_api;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='twitter_score'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'twitter_score', now() )");
    (mysqli_stmt_execute($call));



    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from cr_publicsales_grouped where crowdsale_status='upcoming'");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



    $our_data_count = sizeof($our_data);


    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));


    

    $progressed_coin = 0;
   

    for($i=0; $i<$our_data_count; $i++) {

      $progressed_coin++;
      
        $links = $our_data[$i]['links'];
        $coin_id = $our_data[$i]['cr_id'];
        $coin_key = $our_data[$i]['cr_key'];

        if (!empty($links)) {
            $decodedLinks = json_decode($links, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                // JSON geçerli, $decodedLinks artık bir array
               

                $twitterLink = "";
                foreach ($decodedLinks as $single_link) {
                    if ($single_link['type'] === 'twitter') {
                        $twitterLink = $single_link['value'];
                        $twitterUsername = basename($twitterLink);
                        break;
                    }
                }




                if(strlen($twitterLink) > 0 )
                {


                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://twitterscore.io/api/v1/get_twitter_score?api_key='.$twitter_score_api.'&username='.$twitterUsername.'',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Cookie: csrftoken=OsOWQ6tz3C0QcNTHe7old6TBiMCQ5xqKedrSehewj4oFih1bWnFEsen1YtezT0rF'
                    ),
                    ));

                    $response = curl_exec($curl);


                    $data = json_decode($response, true);

                    $followersCount = null; // Varsayılan olarak null

                    // Success kontrolü
                    if (!empty($data['success']) && $data['success'] === true) {
                        $score = $data['twitter_score'];
          

                        $call = mysqli_prepare($link, "update cr_publicsales set twitter_score = ? where cr_key = ?");

                        mysqli_stmt_bind_param(
                            $call,
                            'ds',
                            $score,
                            $coin_key
                        );
                        $rs2 = (mysqli_stmt_execute($call));

                        if(!$rs2)
                        {
                            error_log(mysqli_error($link),0);
                        }
            
                        


                    }
  


                }




            } else {
                //echo "Geçersiz JSON!";
                sleep(1);
                continue;
            }
        } else {
        
            sleep(1);
            continue;
        }




        if ( (sizeof($our_data) / 100) > 0 &&  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $progressed_coin /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }
        



        sleep(1);





    }


    //If No Error Occured Then Commit Changes To Db
   
        $resp = array("status" => "success");
   

        $changed_count = 0;
        $new_count = 0;

    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='twitter_score'");
    (mysqli_stmt_execute($call));




    echo json_encode($resp, JSON_PRETTY_PRINT);






}


function get_twitter_followers()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global  $link,$twitter_score_api;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='twitter_follower_count'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'twitter_follower_count', now() )");
    (mysqli_stmt_execute($call));



    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from cr_publicsales_grouped where crowdsale_status='upcoming'");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



    $our_data_count = sizeof($our_data);


    $progressed_coin = 0;
   

    for($i=0; $i<$our_data_count; $i++) {

      $progressed_coin++;


      
        $links = $our_data[$i]['links'];
        $coin_id = $our_data[$i]['cr_id'];
        $coin_key = $our_data[$i]['cr_key'];

        if (!empty($links)) {
            $decodedLinks = json_decode($links, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                // JSON geçerli, $decodedLinks artık bir array
               

                $twitterLink = "";
                foreach ($decodedLinks as $single_link) {
                    if ($single_link['type'] === 'twitter') {
                        $twitterLink = $single_link['value'];
                        $twitterUsername = basename($twitterLink);
                        break;
                    }
                }




                if(strlen($twitterLink) > 0 )
                {


                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://twitterscore.io/api/v1/get_twitter_info?api_key='.$twitter_score_api.'&username='.$twitterUsername.'',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Cookie: csrftoken=OsOWQ6tz3C0QcNTHe7old6TBiMCQ5xqKedrSehewj4oFih1bWnFEsen1YtezT0rF'
                    ),
                    ));

                    $response = curl_exec($curl);


                    $data = json_decode($response, true);

                    $followersCount = null; // Varsayılan olarak null

                    // Success kontrolü
                    if (!empty($data['success']) && $data['success'] === true) {
                        $followersCount = $data['followers_count'];
          

                        $call = mysqli_prepare($link, "update cr_publicsales set twitter_followers = ? where cr_key = ?");

                        mysqli_stmt_bind_param(
                            $call,
                            'is',
                            $followersCount,
                            $coin_key
                        );
                        $rs2 = (mysqli_stmt_execute($call));

                        if(!$rs2)
                        {
                            error_log(mysqli_error($link),0);
                        }
            
                        


                    }






                 
                    


                }


              
    





            } else {
                //echo "Geçersiz JSON!";
                sleep(1);
                continue;
            }
        } else {
        
            sleep(1);
            continue;
        }



        sleep(1);

        if ( (sizeof($our_data) / 100) > 0 &&  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $progressed_coin /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }





    }



     
    $resp = array("status" => "success");
   

    $changed_count = 0;
    $new_count = 0;

$call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
(mysqli_stmt_execute($call));

$call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='twitter_follower_count'");
(mysqli_stmt_execute($call));




echo json_encode($resp, JSON_PRETTY_PRINT);






}


function get_twitter_best_followers()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global  $link,$twitter_score_api;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='twitter_best_followers'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'twitter_best_followers', now() )");
    (mysqli_stmt_execute($call));


    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from cr_publicsales_grouped where crowdsale_status='upcoming'");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }



    $our_data_count = sizeof($our_data);


    $progressed_coin = 0;
   

    for($i=0; $i<$our_data_count; $i++) {

      $progressed_coin++;


      
        $links = $our_data[$i]['links'];
        $coin_id = $our_data[$i]['cr_id'];
        $coin_key = $our_data[$i]['cr_key'];

        if (!empty($links)) {
            $decodedLinks = json_decode($links, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                // JSON geçerli, $decodedLinks artık bir array
               

                $twitterLink = "";
                foreach ($decodedLinks as $single_link) {
                    if ($single_link['type'] === 'twitter') {
                        $twitterLink = $single_link['value'];
                        $twitterUsername = basename($twitterLink);
                        break;
                    }
                }




                if(strlen($twitterLink) > 0 )
                {


                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://twitterscore.io/api/v1/get_followers?api_key='.$twitter_score_api.'&username='.$twitterUsername.'',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'GET',
                        CURLOPT_HTTPHEADER => array(
                          'Cookie: csrftoken=OsOWQ6tz3C0QcNTHe7old6TBiMCQ5xqKedrSehewj4oFih1bWnFEsen1YtezT0rF'
                        ),
                      ));

                    $response = curl_exec($curl);


                    $data = json_decode($response, true);

          

                    // Success kontrolü
                    if (!empty($data['success']) && $data['success'] === true) {
                        $top_followers = $data['top_followers'];
                        
                        

                        //burada update etmeyelim, var olan takipciyi silelim
                        //bidaha insert edelim. 
                        //zaten 150 - 200 arası proje var.

                        $call = mysqli_prepare($link, 'delete from twitter_best_followers where cr_id=?');
                        mysqli_stmt_bind_param(
                            $call,
                            'i',
                            $coin_id
                        );
                        (mysqli_stmt_execute($call));



                        for($r = 0; $r < sizeof($top_followers); $r++)
                        {

                        $f_username = $top_followers[$r]['username'];
                        $f_name = $top_followers[$r]['name'];
                        $description = $top_followers[$r]['description'];
                        $x_score = $top_followers[$r]['twitter_score'];
                        $x_id = $top_followers[$r]['twitter_id'];
                        $followers_count = $top_followers[$r]['followers_count'];
                        $profile_image = $top_followers[$r]['profile_image'];


          

                        $call = mysqli_prepare($link, "insert into twitter_best_followers 
                        (cr_id, x_username, x_id, x_name, description, x_score, followers_count, profile_image, create_date)
                         values (?,?,?,?,?,?,?,?,now())");

                        mysqli_stmt_bind_param(
                            $call,
                            'issssiis',
                            $coin_id,
                            $f_username,
                            $x_id,
                            $f_name,
                            $description,
                            $x_score,
                            $followers_count,
                            $profile_image

                        );
                        $rs2 = (mysqli_stmt_execute($call));

                        if(!$rs2)
                        {
                            error_log(mysqli_error($link),0);
                        }
            
                    }
                        


                    }
                    else
                    {
                        error_log(json_encode($data),0);
                    }


                }



            } else {
                //echo "Geçersiz JSON!";
                sleep(1);
                continue;
            }
        } else {
        
            sleep(1);
            continue;
        }



        sleep(1);

        if ( (sizeof($our_data) / 100) > 0 &&  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $progressed_coin /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }






    }




    $resp = array("status" => "success");
   

    $changed_count = 0;
    $new_count = 0;

$call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
(mysqli_stmt_execute($call));

$call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='twitter_best_followers'");
(mysqli_stmt_execute($call));




echo json_encode($resp, JSON_PRETTY_PRINT);




}




function calculate_scores()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    //First get all metrics and metric groups


    global $link, $is_debug;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='scores'");
    (mysqli_stmt_execute($call));


    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'scores', now() )");
    (mysqli_stmt_execute($call));


    $isError = false;

    $call = mysqli_prepare($link, 'select id,metric_group,value_percent from metric_groups_all ');
    // mysqli_stmt_bind_param($call, 'i', $campaign_id);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);



    $metrics = array();
    if ($rs) {

        while ($obj = mysqli_fetch_assoc($rs)) {


            $metrics[] = $obj;
        }
    } else {

        $isError = true;
    }

    if ($isError) {
        $debug_info = "";
        if ($is_debug)
            $debug_info = "Something went wrong while getting campaign deposits.";



        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log("Error details: " . mysqli_error($link), 0);
        die();
    }



    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $metric_groups = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from metric_groups");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        
        $metric_groups[] = $obj2;
    }




      //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $scores_7d_before = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from total_score_7d_change");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        
        $scores_7d_before[$obj2['cr_key']] = $obj2;
    }





    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from coindata_all");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $coin_metric_specials = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT coin_id, metric_id from metric_specials");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        //$coin_group_scores[] = $obj2;
        $coin_metric_specials[$obj2['coin_id'].'_'.$obj2['metric_id']] = $obj2;
    }



    $ai_metric_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from metric_detail where active = 1");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $ai_metric_data[$obj2['coin_id'].'_'.$obj2['metric_type_id']][] = $obj2;
    }






    $our_data_count = sizeof($our_data);
    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));


    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $coin_scores = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid, metric_subgroup from coin_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        $coin_scores[$obj2['geckoid']][$obj2['metric_subgroup']] = true;

        //  $coin_scores[] = $obj2;
    }


    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $coin_group_scores = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid, metric_group from coin_group_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        //$coin_group_scores[] = $obj2;
        $coin_group_scores[$obj2['geckoid'].'_'.$obj2['metric_group']] = $obj2;
    }


    $certik_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT certiKProjectId, tokenTickers, score, codeSecurity, communityTrust,
    fundamentalHealth, governanceStrength, marketStability, operationalResilience, coinGeckoSlug
     from certik_security_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        $certik_data[$obj2['certiKProjectId']] = $obj2;
        //  $certik_data[] = $obj2;
    }


    $ai_score_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from metric_main where active = 1");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        $ai_score_data[$obj2['coin_id']][$obj2['metric_type_id']] = $obj2;
       
    }





    //Now loop all coins and calculate scores 


    $success = true;
    $changed_count = 0;
    $new_count = 0;


    $updateCases = [];
    $updateIds = [];
    $insertValues = [];


    $specials_updateCases = [];
    $specials_updateIds = [];
    $specials_insertValues = [];


    $coin_count = sizeof($our_data);

    for ($k = 0; $k < $coin_count; $k++) {
        $geckoid = $our_data[$k]['geckoid']; //adı geckoid kaldı ancak aslında burada cryptorank key alanı var
        $symbol = $our_data[$k]['symbol'];
        $certik_project_id = $our_data[$k]['certik_project_id']; //certik eşleştirme kolonu olarka bunu kullanıyoruz.

        $cr_id =  $our_data[$k]['cr_id'];

        $group_score_1 = 0;
        $group_score_2 = 0;
        $group_score_3 = 0;
        $group_score_4 = 0;
        $group_score_5 = 0;
        $group_score_6 = 0;
        $total_score = 0;


        for ($i = 0; $i < sizeof($metrics); $i++) {


            //Eger asagıda metric if else leri içinde değişmedikçe metric_type = 0 olsun
            //metric_type = 0 -> value
             //metric_type = 1 -> json_array
            $metric_type = 0;   

            $is_it_update = false;
            $specials_is_it_update = false;

            if (isset($coin_scores[$geckoid][$metrics[$i]['id']])) {
                $is_it_update = true;
            }



            $uniq_key = $cr_id.'_'.$metrics[$i]['id'];

            if(isset($coin_metric_specials[$uniq_key]))
            {
                $specials_is_it_update = true;
            }





            /*

            //todo burada bir de tumunu for ile donemden mysql sorugu ilse deneyerek test edelmi sureyi
            for ($s = 0; $s < sizeof($coin_scores); $s++) {

                if (($geckoid == $coin_scores[$s]['geckoid']) && ($metrics[$i]['id'] ==  $coin_scores[$s]['metric_subgroup'])) {

                    $is_it_update = true;
                    //   array_splice($coin_scores, $s, 1);
                    break;
                }
            }

            */







            $metric_score = 0;


            //for demo purposes 
            //if group_id == 1 (Tokenomics) make all scores 75
            //if group_id == 2 (Vesting) make all scores 80
            //if group_id == 3 (Security) make all scores 85
            //if group_id == 4 (Socials) make all scores 70
            //if group_id == 5 (Market) make all scores 75
            //if group_id == 6 (Fundamentals) make all scores 90


            $group_id = $metrics[$i]['metric_group'];
            $metric_weight = $metrics[$i]['value_percent'];
            $metric_id = $metrics[$i]['id'];



            //security istisna tanımlaylaım
            if ($group_id == 3) {

                //   for ($s = 0; $s < sizeof($certik_data); $s++) {


                if (isset($certik_project_id))
                    $certik_row_data = $certik_data[$certik_project_id];


                //Code Security
                if ($metric_id == 3) {
                    if (isset($certik_project_id) && isset($certik_row_data['codeSecurity'])) {
                        $metric_score = $certik_row_data['codeSecurity'];
                    } else {
                        $metric_score = 0;
                    }

                     $metric_special_key = "code_security";
                     $metric_special_value = $metric_score;


                }
                //Community Trust
                else if ($metric_id == 4) {

                    if (isset($certik_project_id) && $certik_row_data['communityTrust']) {
                        $metric_score = $certik_row_data['communityTrust'];
                    } else {
                        $metric_score = 0;
                    }

                     $metric_special_key = "community_trust";
                     $metric_special_value = $metric_score;
                }
                //Fundamental Health
                else if ($metric_id == 17) {

                    if (isset($certik_project_id) && $certik_row_data['fundamentalHealth']) {
                        $metric_score = $certik_row_data['fundamentalHealth'];
                    } else {
                        $metric_score = 0;
                    }

                    $metric_special_key = "fundamental_health";
                    $metric_special_value = $metric_score;


                }
                //Governance Strength
                else if ($metric_id == 18) {

                    if (isset($certik_project_id) && $certik_row_data['governanceStrength']) {
                        $metric_score = $certik_row_data['governanceStrength'];
                    } else {
                        $metric_score = 0;
                    }


                    $metric_special_key = "governance_strength";
                    $metric_special_value = $metric_score;

                    
                }
                //Market Stability
                else if ($metric_id == 19) {

                    if (isset($certik_project_id) && $certik_row_data['marketStability']) {
                        $metric_score = $certik_row_data['marketStability'];
                    } else {
                        $metric_score = 0;
                    }


                    $metric_special_key = "market_stability";
                    $metric_special_value = $metric_score;


                }
                //Operational Resilience
                else if ($metric_id == 20) {

                    if (isset($certik_project_id) && $certik_row_data['operationalResilience']) {
                        $metric_score = $certik_row_data['operationalResilience'];
                    } else {
                        $metric_score = 0;
                    }


                    $metric_special_key = "operational_resilience";
                    $metric_special_value = $metric_score;


                }

                $group_score_3 =  $certik_row_data['score'];


                //  break;


                // }
            } else {


                //Metrics

                // 1 - Market Cap / FDV Ratio
                if ($metric_id == 1) {
                    //burada fdv hesaplanamıyorsa score olarak 100 verelim.
                    if ($our_data[$k]['fdv'] == NULL || $our_data[$k]['fdv'] == 0) {
                        $ratio = 100;
                    } else {

                        $ratio = $our_data[$k]['marketcap'] / $our_data[$k]['fdv'] * 100;
                    }

                    $metric_special_key = "mcap_fdv_ratio";
                    $metric_special_value = $ratio;


                    if($ratio < 50)
                    {
                        $metric_score = 49;
                    }
                    else  if($ratio < 65)
                    {
                        $metric_score = 64;
                    }
                    else  if($ratio < 75)
                    {
                        $metric_score = 74;
                    }
                    else  if($ratio < 95)
                    {
                        $metric_score = 89;
                    }
                    else
                    $metric_score = 100;
                }

                //2 - Max Supply 
                else if ($metric_id == 2) {

                    $max_supply = 0;
                    if ($our_data[$k]['max_supply'] == NULL) {
                        $metric_score = 65;
                    } else {
                         $max_supply = $our_data[$k]['max_supply'];
                        $metric_score = 100;
                    }

                     $metric_special_key = "max_supply";
                     $metric_special_value = $max_supply;


                }


                //5 - 24H Trade Volume
                else if ($metric_id == 5) {

                    $total_volume = $our_data[$k]['total_volume'];


                    if ($total_volume < 100000) {
                        $metric_score = 49;
                    } else if ($total_volume < 200000) {
                        $metric_score = 64;
                    } else if ($total_volume < 500000) {
                        $metric_score = 74;
                    } else if ($total_volume < 1000000) {
                        $metric_score = 89;
                    } else {
                        $metric_score = 100;
                    }

                     $metric_special_key = "total_volume";
                     $metric_special_value = $total_volume;


                } else if ($metric_id == 6) {

                    //boşsa ne yapalım?
                    if ($our_data[$k]['best_cex_rank'] != NULL) {

                        $best_cex_rank = $our_data[$k]['best_cex_rank'];

                        if ($best_cex_rank < 6)
                            $metric_score = 100;
                        else if ($best_cex_rank < 11)
                            $metric_score = 89;
                        else if ($best_cex_rank < 21)
                            $metric_score = 74;
                        else if ($best_cex_rank < 51)
                            $metric_score = 64;
                        else
                            $metric_score = 49;
                    } else
                        $metric_score = 50;


                     $metric_special_key = "best_cex_rank";
                     $metric_special_value = $best_cex_rank;


                } else if ($metric_id == 7) {

                    //boşsa ne yapalım?
                    if ($our_data[$k]['best_dex_rank'] != NULL) {

                        $best_cex_rank = $our_data[$k]['best_dex_rank'];

                        if ($best_cex_rank < 6)
                            $metric_score = 100;
                        else if ($best_cex_rank < 11)
                            $metric_score = 89;
                        else if ($best_cex_rank < 21)
                            $metric_score = 74;
                        else if ($best_cex_rank < 51)
                            $metric_score = 64;
                        else
                            $metric_score = 49;
                    } else
                        $metric_score = 50;

                     $metric_special_key = "best_dex_rank";
                     $metric_special_value = $best_cex_rank;


                } else if ($metric_id == 8) {
                    //boşsa ne yapalım?
                    if ($our_data[$k]['cex_count'] != NULL) {

                        $cex_count = $our_data[$k]['cex_count'];

                        if ($cex_count > 6)
                            $metric_score = 100;
                        else if ($cex_count > 4)
                            $metric_score = 89;
                        else if ($cex_count > 2)
                            $metric_score = 74;
                        else if ($cex_count > 0)
                            $metric_score = 64;
                        else
                            $metric_score = 49;
                    } else
                        $metric_score = 0;

                    $metric_special_key = "cex_count";
                    $metric_special_value = $cex_count;


                } else if ($metric_id == 9) {

                    //boşsa ne yapalım?
                    if ($our_data[$k]['dex_count'] != NULL) {

                        $cex_count = $our_data[$k]['dex_count'];

                        if ($cex_count > 6)
                            $metric_score = 100;
                        else if ($cex_count > 4)
                            $metric_score = 89;
                        else if ($cex_count > 2)
                            $metric_score = 74;
                        else if ($cex_count > 0)
                            $metric_score = 64;
                        else
                            $metric_score = 49;
                    } else
                        $metric_score = 0;

                    $metric_special_key = "dex_count";
                    $metric_special_value = $cex_count;


                } else if ($metric_id == 10) {

                    //inflation rate
                    $metric_score = 100;
                } else if ($metric_id == 11) {

                    // versting schedule
                    $metric_score = 100;

                    $ratio = 0;

                    //circ supply / total_supply

                      //burada fdv hesaplanamıyorsa score olarak 100 verelim.
                      if ($our_data[$k]['total_supply'] == NULL || $our_data[$k]['total_supply'] == 0) {
                        $metric_score = 49;
                    } else {

                        $ratio = $our_data[$k]['circulating_supply'] / $our_data[$k]['total_supply'] * 100;


                        if($ratio < 50)
                        {
                            $metric_score = 49;
                        }
                        else  if($ratio < 65)
                        {
                            $metric_score = 64;
                        }
                        else  if($ratio < 75)
                        {
                            $metric_score = 74;
                        }
                        else  if($ratio < 90)
                        {
                            $metric_score = 89;
                        }
                        else
                        $metric_score = 100;



                    }


                      $metric_special_key = "vesting_schedule";
                      $metric_special_value = $ratio;






                } else if ($metric_id == 12) {

                    $ratio = $our_data[$k]['emission_1y'];

                    if($ratio > 30)
                        {
                            $metric_score = 49;
                        }
                        else  if($ratio > 20)
                        {
                            $metric_score = 64;
                        }
                        else  if($ratio < 10)
                        {
                            $metric_score = 74;
                        }
                        else  if($ratio < 5)
                        {
                            $metric_score = 89;
                        }
                        else
                        $metric_score = 100;


                    $metric_special_key = "emission_score_1y";
                    $metric_special_value = $ratio;


                   
                } else if ($metric_id == 13) {

                    // total value locked
                    $metric_score = 100;
                } else if ($metric_id == 14) {

                    // risk reward rating
                  
                    $marketcap = $our_data[$k]['marketcap'];



                    if($marketcap < 10000000)
                    {
                        $metric_score = 100;
                    }
                    else  if($marketcap < 50000000)
                    {
                        $metric_score = 89;
                    }
                    else  if($marketcap < 400000000)
                    {
                        $metric_score = 74;
                    }
                    else  if($marketcap < 1000000000)
                    {
                        $metric_score = 64;
                    }
                    else
                    $metric_score = 49;


                    $metric_special_key = "risk_reward_rating";
                    $metric_special_value = $marketcap;

                    





                } else if ($metric_id == 15) {

                    //TVL to Marketcap
                    $metric_score = 100;
                } else if ($metric_id == 16) {

                    //TVL Growth
                    $metric_score = 100;
                } else if ($metric_id == 23) {

                    if ($our_data[$k]['galaxy_score'] != NULL) {

                        $galaxy_score = $our_data[$k]['galaxy_score'];

                        $metric_score = $galaxy_score;
                    } else
                        $metric_score = 0;

                    $metric_special_key = "risk_reward_rating";
                    $metric_special_value = $metric_score;


                } else if ($metric_id == 24) {

                    if ($our_data[$k]['alt_rank'] != NULL) {

                        $alt_rank = $our_data[$k]['galaxy_score'];

                        $alt_rank_percent = $alt_rank / $coin_count * 100;

                        if ($alt_rank_percent < 10) {
                            $metric_score = 100;
                        } else if ($alt_rank_percent < 25) {
                            $metric_score = 89;
                        } else if ($alt_rank_percent < 50) {
                            $metric_score = 74;
                        } else if ($alt_rank_percent < 75) {
                            $metric_score = 64;
                        } else {
                            $metric_score = 50;
                        }
                    } else
                        $metric_score = 0;


                    $metric_special_key = "alt_rank";
                    $metric_special_value = $metric_score;


                } else if ($metric_id == 25) {
                    //engaement score
                    $metric_score = 100;
                } else if ($metric_id == 26) {
                    //creators score
                    $metric_score = 100;
                } else if ($metric_id == 27) {

                    if ($our_data[$k]['sentiment'] != NULL) {

                        $sentiment = $our_data[$k]['sentiment'];

                        $metric_score = $sentiment;
                    } else
                        $metric_score = 0;

                    $metric_special_key = "sentiment";
                    $metric_special_value = $metric_score;


                } else if ($metric_id == 28) {

                    $cg_followers = $our_data[$k]['gecko_portfolio_count'];

                 if ($cg_followers == NULL)
                        $cg_followers = 0;

                    if ($cg_followers < 1000) {
                        $metric_score = 49;
                    } else if ($cg_followers < 3000) {
                        $metric_score = 64;
                    } else if ($cg_followers < 10000) {
                        $metric_score = 74;
                    } else if ($cg_followers < 30000) {
                        $metric_score = 89;
                    } else {
                        $metric_score = 100;
                    }

                    $metric_special_key = "gecko_portfolio_count";
                    $metric_special_value = $cg_followers;


                } else if ($metric_id == 30) {
                    //social dominance
                    $lunar_social_dom = $our_data[$k]['social_dominance'];

                    
                    if ($lunar_social_dom == NULL)
                    $lunar_social_dom = 0;

                if ($lunar_social_dom < 1) {
                    $metric_score = 49;
                } else if ($lunar_social_dom < 5) {
                    $metric_score = 64;
                } else if ($lunar_social_dom < 10) {
                    $metric_score = 74;
                } else if ($lunar_social_dom < 20) {
                    $metric_score = 89;
                } else {
                    $metric_score = 100;
                }

                $metric_special_key = "social_dominance";
                $metric_special_value = $lunar_social_dom;

                    
                    
                } else if ($metric_id == 29) {

                    //mention score
                    $lunar_social_vol = $our_data[$k]['social_volume_24h'];

                    
                    if ($lunar_social_vol == NULL)
                    $lunar_social_vol = 0;

                if ($lunar_social_vol < 100) {
                    $metric_score = 49;
                } else if ($lunar_social_vol < 500) {
                    $metric_score = 64;
                } else if ($lunar_social_vol < 1000) {
                    $metric_score = 74;
                } else if ($lunar_social_vol < 5000) {
                    $metric_score = 89;
                } else {
                    $metric_score = 100;
                }


                $metric_special_key = "social_volume_24h";
                $metric_special_value = $lunar_social_vol;



                } else if ($metric_id == 31) {

                    //engament to follower ratio
                    $metric_score = 100;
                } else if ($metric_id == 34) {

                    //cateogry hype
                    $metric_score = 100;
                } else if ($metric_id == 35) {

                    //token use case
                    $metric_score = 100;
                } else if ($metric_id == 36) {

                    if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

                
                   $metric_type = 1;
                   $metric_special_key = "token_use_case";
                   $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               

                    //token use case
                } else if ($metric_id == 37) {

                    //fundraising score
                    $metric_score = 100;
                } else if ($metric_id == 38) {

                    //def or inf
                    if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

            
                $metric_type = 1;
                $metric_special_key = "def_or_inf";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               
             


                } else if ($metric_id == 39) {

                    //cross-chain interperability
                    $metric_score = 100;

               
                $metric_type = 1;
                $metric_special_key = "cross_chain_rating";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               
               



                } else if ($metric_id == 40) {

                    //token redist
                    if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

         
                $metric_type = 1;
                $metric_special_key = "token_redist_score";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               

                } else if ($metric_id == 41) {

                    //token buyback mech
                     //token redist
                     if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

                $uniq_key = $cr_id.'_'.$metrics[$i]['id'];

                if(!isset( $ai_metric_data[$uniq_key]))
                 $metric_special_value = "";


              
                $metric_type = 1;
                $metric_special_key = "token_buyback";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               
             

                    
                } else if ($metric_id == 42) {

                    //revenue sharing
                    if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

            

               
                $metric_type = 1;
                $metric_special_key = "revenue_sharing";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               
              
                    
                } else if ($metric_id == 43) {

                    //project dev status
                    $metric_score = 100;
                } else if ($metric_id == 44) {

                    //real world application
                    $metric_score = 100;
                } 

                else if ($metric_id == 50) {

                    if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

               
            
                $metric_type = 1;
                $metric_special_key = "team_anonymity";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               
               

                    //token use case
                }

                else if ($metric_id == 52) {

                    if (isset($ai_score_data[$cr_id][$metric_id]['metric_score'])) {
                        $metric_score = $ai_score_data[$cr_id][$metric_id]['metric_score'];
                    } else {
                        $metric_score = 0; // ya da default bir değer
                    }

               

               
                $metric_type = 1;
                $metric_special_key = "dao_governance";
                $metric_special_value =  get_ai_special_data($cr_id, $ai_metric_data, $metrics[$i]['id']);
               
              

                    //token use case
                }
                else {
                    $metric_score = 100;
                }
            }






            if ($group_id == 1) {

                //  $metric_score = 76;

                $group_score_1 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 2) {

                //  $metric_score = 80;

                $group_score_2 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 3) {

                //  $metric_score = 85;

                //$group_score_3 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 4) {

                //  $metric_score = 70;

                $group_score_4 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 5) {

                //  $metric_score = 75;

                $group_score_5 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 6) {

                //  $metric_score = 90;

                $group_score_6 += ($metric_weight *  $metric_score / 100);
            }



            if ($is_it_update) {

                $changed_count++;


                // Güncellenmesi gereken veriler
                $updateCases[] = sprintf(
                    "WHEN geckoid = '%s' AND metric_subgroup = %d THEN %d",
                    $geckoid,
                    $metric_id,
                    $metric_score
                );
                $updateIds[] = sprintf("('%s', %d)", $geckoid, $metric_id);
            } else {

                $new_count++;


                // Yeni eklenmesi gereken veriler
                $insertValues[] = sprintf(
                    "('%s', %d, %d, NOW())",
                    $geckoid,
                    $metric_id,
                    $metric_score
                );
            }





              if ($specials_is_it_update) {


                // Güncellenmesi gereken veriler
                $specials_updateCases[] = sprintf(
                    "WHEN coin_id = %d AND metric_id = %d AND metric_key= '%s' THEN '%s' ",
                    $cr_id,
                    $metric_id,
                    $metric_special_key,
                    $metric_special_value
                );
                $specials_updateIds[] = sprintf("(%d, %d, '%s')", $cr_id, $metric_id, $metric_special_key);
            } else {

                // Yeni eklenmesi gereken veriler
                $specials_insertValues[] = sprintf(
                    "(%d, %d, '%s', '%s', %d, NOW())",
                    $cr_id,
                    $metric_id,
                    $metric_special_key,
                    $metric_special_value,
                    $metric_type
                );
            }





            /*

            if ($is_it_update) {

                $changed_count++;

                $call = mysqli_prepare($link, 'update coin_scores
            set score=?, update_date=now() where geckoid=? and metric_subgroup=?');
                mysqli_stmt_bind_param(
                    $call,
                    'isi',
                    $metric_score,
                    $geckoid,
                    $metric_id
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $new_count++;

                $call = mysqli_prepare($link, 'insert into coin_scores
            (geckoid, metric_subgroup, score  ) 
            values (?, ?, ?)');
                mysqli_stmt_bind_param(
                    $call,
                    'sii',
                    $geckoid,
                    $metric_id,
                    $metric_score
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id1=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            }

            */
        }













        $group_updateCases = [];
        $group_updateIds = [];
        $group_insertValues = [];

        for ($c = 0; $c < sizeof($metric_groups); $c++) {


            $is_it_update = false;
            $metric_group_id = $metric_groups[$c]['id'];
            $group_weight = $metric_groups[$c]['value'];

            $group_score = ${"group_score_" . $metric_groups[$c]['id']};


            $total_score += $group_score * $group_weight / 100;



            $uniq_key = $geckoid.'_'.$metric_groups[$c]['id'];

            if(isset($coin_group_scores[$uniq_key]))
            {
                $is_it_update = true;
            }



            /*
            for ($s = 0; $s < sizeof($coin_group_scores); $s++) {
                if ($geckoid == $coin_group_scores[$s]['geckoid'] && $metric_groups[$c]['id'] ==  $coin_group_scores[$s]['metric_group']) {

                    $is_it_update = true;
                    break;
                }
            }
            */



            if ($is_it_update) {

      


                // Güncellenmesi gereken veriler
                $group_updateCases[] = sprintf(
                    "WHEN geckoid = '%s' AND metric_group = %d THEN %d",
                    $geckoid,
                    $metric_group_id,
                    $group_score
                );
                $group_updateIds[] = sprintf("('%s', %d)", $geckoid, $metric_group_id);
            } else {

 


                // Yeni eklenmesi gereken veriler
                $group_insertValues[] = sprintf(
                    "('%s', %d, %d, NOW())",
                    $geckoid,
                    $metric_group_id,
                    $group_score
                );
            }




            /*
            

            if ($is_it_update) {

                $call = mysqli_prepare($link, 'update coin_group_scores
        set score=round(?), update_date=now() where geckoid=? and metric_group=?');
                mysqli_stmt_bind_param(
                    $call,
                    'isi',
                    $group_score,
                    $geckoid,
                    $metric_group_id
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id2=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $call = mysqli_prepare($link, 'insert into coin_group_scores
        (geckoid, metric_group, score  ) 
        values (?, ?, round(?))');
                mysqli_stmt_bind_param(
                    $call,
                    'sii',
                    $geckoid,
                    $metric_group_id,
                    $group_score
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id3=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            }

            */


        }

        bulk_update_db("group_scores",$group_updateCases,$group_updateIds,$group_insertValues);

        $change_7d = 0;

        if(isset( $scores_7d_before[$geckoid]['score_7d_ago']))
        {
        $score_7d_ago = $scores_7d_before[$geckoid]['score_7d_ago'];
       

        if(isset($score_7d_ago) && $score_7d_ago != 0)
        {

            $change_7d = (($total_score - $score_7d_ago) / $score_7d_ago ) * 100;

        }
        }


   

        $call = mysqli_prepare($link, 'update coindata
    set total_score=round(?), score_change_7d=round(?, 2)  where geckoid=? ');
        mysqli_stmt_bind_param(
            $call,
            'ids',
            $total_score,
            $change_7d,
            $geckoid
        );
        $rs = (mysqli_stmt_execute($call));

        if (!$rs) {
            $success = false;
            error_log("gecko_id4=" . $geckoid, 0);
            error_log(mysqli_error($link), 0);
        }




        //  if($k == 10)
        //  break;

        if (($k + 1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $k /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


        if (($k > 0 && $k % 300 == 0 ) || $k == ($coin_count - 1) ) {

            bulk_update_db("scores", $updateCases, $updateIds, $insertValues);

            $updateCases = [];
            $updateIds = [];
            $insertValues = [];


             bulk_update_db("specials", $specials_updateCases, $specials_updateIds, $specials_insertValues);
            
            $specials_updateCases = [];
            $specials_updateIds = [];
            $specials_insertValues = [];
        }
    }



    //score history tablosunu dolduralım.

    $call = mysqli_prepare($link, "
    insert into total_score_history (cr_id,cr_key,score,score_date) 
    select cr_id, geckoid as cr_key, (select ifnull(total_score ,0)) as score,
     update_date as score_date from coindata
    ");
    (mysqli_stmt_execute($call));

    







    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }


    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='scores'");
    (mysqli_stmt_execute($call));

    recalculate_total_score();

    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function recalculate_total_score()
{

                global $link;

                $gecko_id = "bitcoin";

                 //bu aşamada total_Score u yeniden hesaplayıp coindata içine yazalım. Yani total_score calculated degeri
                //bu deger ile ezilecek ve hiç bir yerde olmayacak manual skorlanan coinler için.

                $query =  "SELECT 
                SUM(c.score * (m.value / t.total_value)) AS total_score
                FROM coin_group_scores_all c
                JOIN metric_groups m 
                ON m.id = c.metric_group AND m.isactive = 1
                JOIN (
                SELECT SUM(value) AS total_value
                FROM metric_groups
                WHERE isactive = 1
                ) t
                WHERE c.geckoid = ?";

                 $call = mysqli_prepare($link, $query);
                 mysqli_stmt_bind_param( $call,'s', $gecko_id);
                (mysqli_stmt_execute($call));
                $rs = mysqli_stmt_get_result($call);
                

                 if ($rs) {

                    while ($obj = mysqli_fetch_assoc($rs)) {


                        $total_score = round($obj['total_score']);
        
                    }
                }



                 $call = mysqli_prepare($link, "update coindata set total_score=? where geckoid=?");
                  mysqli_stmt_bind_param( $call,'is', $total_score,$gecko_id);
                (mysqli_stmt_execute($call));



        


}


function calculate_ico_scores()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    //First get all metrics and metric groups


    global $link, $is_debug;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='ico_scores'");
    (mysqli_stmt_execute($call));


    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'ico_scores', now() )");
    (mysqli_stmt_execute($call));



    $isError = false;

    $call = mysqli_prepare($link, 'select id,metric_group,value_percent from ico_metric_groups_all ');
    // mysqli_stmt_bind_param($call, 'i', $campaign_id);
    (mysqli_stmt_execute($call));
    $rs = mysqli_stmt_get_result($call);



    $metrics = array();
    if ($rs) {

        while ($obj = mysqli_fetch_assoc($rs)) {


            $metrics[] = $obj;
        }
    } else {

        $isError = true;
    }

    if ($isError) {
        $debug_info = "";
        if ($is_debug)
            $debug_info = "Something went wrong while getting campaign deposits.";



        http_response_code(404);
        echo json_encode(['error' => "Invalid Request!", 'detail' => $debug_info]);
        error_log("Error details: " . mysqli_error($link), 0);
        die();
    }



    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $metric_groups = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from ico_metric_groups");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        
        $metric_groups[] = $obj2;
    }







    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from ico_coindata_all");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);
    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));


    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $coin_scores = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid, metric_subgroup from ico_coin_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        $coin_scores[$obj2['geckoid']][$obj2['metric_subgroup']] = true;

        //  $coin_scores[] = $obj2;
    }


    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $coin_group_scores = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT geckoid, metric_group from ico_coin_group_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        //$coin_group_scores[] = $obj2;
        $coin_group_scores[$obj2['geckoid'].'_'.$obj2['metric_group']] = $obj2;
    }



    $certik_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT certiKProjectId, tokenTickers, score, codeSecurity, communityTrust,
    fundamentalHealth, governanceStrength, marketStability, operationalResilience, coinGeckoSlug
     from certik_security_scores");
    while ($obj2 = mysqli_fetch_assoc($rs)) {

        $certik_data[$obj2['certiKProjectId']] = $obj2;
        //  $certik_data[] = $obj2;
    }


    $obj2 = array();
    $rs = mysqli_query($link, "SELECT 
    MIN(twitter_score) AS min_twitter_score,
    MAX(twitter_score) AS max_twitter_score
FROM 
    cr_publicsales
WHERE 
    crowdsale_status = 'upcoming'");

    while ($obj2 = mysqli_fetch_assoc($rs)) {

       $twitter_min = $obj2['min_twitter_score'];
       $twitter_max = $obj2['max_twitter_score'];

       if(!isset($twitter_max) )
       $twitter_max = 0;

       if(!isset($twitter_min) )
       $twitter_min = 0;




        //  $certik_data[] = $obj2;
    }


    





    //Now loop all coins and calculate scores 


    $success = true;
    $changed_count = 0;
    $new_count = 0;


    $updateCases = [];
    $updateIds = [];
    $insertValues = [];

    $coin_count = sizeof($our_data);

    for ($k = 0; $k < $coin_count; $k++) {
        $geckoid = $our_data[$k]['cr_key']; //adı geckoid kaldı ancak aslında burada cryptorank key alanı var
        $symbol = $our_data[$k]['symbol'];
        $certik_project_id = $our_data[$k]['certik_project_id']; //certik eşleştirme kolonu olarka bunu kullanıyoruz.


        $group_score_1 = 0;
        $group_score_2 = 0;
        $group_score_3 = 0;
        $group_score_4 = 0;
        $group_score_5 = 0;
        $group_score_6 = 0;
        $total_score = 0;


        for ($i = 0; $i < sizeof($metrics); $i++) {



            $is_it_update = false;

            if (isset($coin_scores[$geckoid][$metrics[$i]['id']])) {
                $is_it_update = true;
            }


            /*

            //todo burada bir de tumunu for ile donemden mysql sorugu ilse deneyerek test edelmi sureyi
            for ($s = 0; $s < sizeof($coin_scores); $s++) {

                if (($geckoid == $coin_scores[$s]['geckoid']) && ($metrics[$i]['id'] ==  $coin_scores[$s]['metric_subgroup'])) {

                    $is_it_update = true;
                    //   array_splice($coin_scores, $s, 1);
                    break;
                }
            }

            */



            $metric_score = 0;


            //for demo purposes 
            //if group_id == 1 (Tokenomics) make all scores 75
            //if group_id == 2 (Vesting) make all scores 80
            //if group_id == 3 (Security) make all scores 85
            //if group_id == 4 (Socials) make all scores 70
            //if group_id == 5 (Market) make all scores 75
            //if group_id == 6 (Fundamentals) make all scores 90


            $group_id = $metrics[$i]['metric_group'];
            $metric_weight = $metrics[$i]['value_percent'];
            $metric_id = $metrics[$i]['id'];



                //Metrics

                // 105 - Inıtial Market Cap
                if ($metric_id == 105) {
                  
                    $total_volume = $our_data[$k]['initial_marketcap'];


                    if ($total_volume < 500000) {
                        $metric_score = 100;
                    } else if ($total_volume < 1000000) {
                        $metric_score = 89;
                    } else if ($total_volume < 2000000) {
                        $metric_score = 74;
                    } else if ($total_volume < 5000000) {
                        $metric_score = 64;
                    } else {
                        $metric_score = 49;
                    }
                }

                //106 fuınding score
               
                else if ($metric_id == 106) {
                  
                    $total_volume = $our_data[$k]['crowdsale_raise'];


                    if ($total_volume < 500000) {
                        $metric_score = 49;
                    } else if ($total_volume < 1000000) {
                        $metric_score = 64;
                    } else if ($total_volume < 2000000) {
                        $metric_score = 74;
                    } else if ($total_volume < 5000000) {
                        $metric_score = 89;
                    } else {
                        $metric_score = 100;
                    }
                }



                //53 - Total Supply Scoring
                else if ($metric_id == 107) {

                    $lp_count = $our_data[$k]['lp_count'];
                    $lp_tier = $our_data[$k]['lp_best_tier'];


                    if ($lp_count > 4) {
                        $lp_count_score = 100;
                    } else if ($lp_count > 2) {
                        $lp_count_score = 89;
                    } else if ($lp_count > 1) {
                        $lp_count_score = 74;
                    } else if ($lp_count > 0) {
                        $lp_count_score = 64;
                    } else {
                        $lp_count_score = 49;
                    }


                    if ($lp_tier == 1) {
                        $lp_tier_score = 100;
                    } else if ($lp_tier == 2) {
                        $lp_tier_score = 89;
                    } else if ($lp_tier == 3) {
                        $lp_tier_score = 74;
                    } else if ($lp_tier == 4) {
                        $lp_tier_score = 64;
                    } else {
                        $lp_tier_score = 49;
                    }



                    $metric_score = $lp_tier_score * 0.75 + $lp_count_score * 0.25;


                } 

                else if ($metric_id == 108) {


                    $fund_tier = $our_data[$k]['fund_best_tier'];


                    if ($fund_tier > 4) {
                        $metric_score = 49;
                    } else if ($fund_tier  == 4) {
                        $metric_score = 64;
                    } else if ($fund_tier == 3) {
                        $metric_score = 74;
                    } else if ($fund_tier == 2 ) {
                        $metric_score = 89;
                    } else if ($fund_tier == 1) {
                        $metric_score = 100;
                    }


                   
                }


                else if ($metric_id == 109) {

                    $x_score = $our_data[$k]['twitter_score'];


                    if($twitter_max - $twitter_min == 0)
                    {

                        $metric_score = 49;
                        continue;
                    }


                    $normalized_score = (($x_score - $twitter_min) / ($twitter_max - $twitter_min)) * 100;

                    $metric_score = $normalized_score;


                    if ($normalized_score > 89) {
                        $metric_score = 100;
                    } else if ($normalized_score > 74) {
                        $metric_score = 89;
                    } else if ($normalized_score > 64) {
                        $metric_score = 74;
                    } else if ($normalized_score > 49) {
                        $metric_score = 64;
                    } else {
                        $metric_score = 49;
                    }




                }
      
              
            






            if ($group_id == 1) {

                //  $metric_score = 76;

                $group_score_1 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 2) {

                //  $metric_score = 80;

                $group_score_2 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 3) {

                //  $metric_score = 85;

                $group_score_3 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 4) {

                //  $metric_score = 70;

                $group_score_4 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 5) {

                //  $metric_score = 75;

                $group_score_5 += ($metric_weight *  $metric_score / 100);
            } else if ($group_id == 6) {

                //  $metric_score = 90;

                $group_score_6 += ($metric_weight *  $metric_score / 100);
            }



            if ($is_it_update) {

                $changed_count++;


                // Güncellenmesi gereken veriler
                $updateCases[] = sprintf(
                    "WHEN geckoid = '%s' AND metric_subgroup = %d THEN %d",
                    $geckoid,
                    $metric_id,
                    $metric_score
                );
                $updateIds[] = sprintf("('%s', %d)", $geckoid, $metric_id);
            } else {

                $new_count++;


                // Yeni eklenmesi gereken veriler
                $insertValues[] = sprintf(
                    "('%s', %d, %d, NOW())",
                    $geckoid,
                    $metric_id,
                    $metric_score
                );
            }








            /*

            if ($is_it_update) {

                $changed_count++;

                $call = mysqli_prepare($link, 'update coin_scores
            set score=?, update_date=now() where geckoid=? and metric_subgroup=?');
                mysqli_stmt_bind_param(
                    $call,
                    'isi',
                    $metric_score,
                    $geckoid,
                    $metric_id
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $new_count++;

                $call = mysqli_prepare($link, 'insert into coin_scores
            (geckoid, metric_subgroup, score  ) 
            values (?, ?, ?)');
                mysqli_stmt_bind_param(
                    $call,
                    'sii',
                    $geckoid,
                    $metric_id,
                    $metric_score
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id1=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            }

            */
        }



    








        $group_updateCases = [];
        $group_updateIds = [];
        $group_insertValues = [];

        for ($c = 0; $c < sizeof($metric_groups); $c++) {


            $is_it_update = false;
            $metric_group_id = $metric_groups[$c]['id'];
            $group_weight = $metric_groups[$c]['value'];

            $group_score = ${"group_score_" . $metric_groups[$c]['id']};


            $total_score += $group_score * $group_weight / 100;



            $uniq_key = $geckoid.'_'.$metric_groups[$c]['id'];

            if(isset($coin_group_scores[$uniq_key]))
            {
                $is_it_update = true;
            }



            /*
            for ($s = 0; $s < sizeof($coin_group_scores); $s++) {
                if ($geckoid == $coin_group_scores[$s]['geckoid'] && $metric_groups[$c]['id'] ==  $coin_group_scores[$s]['metric_group']) {

                    $is_it_update = true;
                    break;
                }
            }
            */



            if ($is_it_update) {

      


                // Güncellenmesi gereken veriler
                $group_updateCases[] = sprintf(
                    "WHEN geckoid = '%s' AND metric_group = %d THEN %d",
                    $geckoid,
                    $metric_group_id,
                    $group_score
                );
                $group_updateIds[] = sprintf("('%s', %d)", $geckoid, $metric_group_id);
            } else {

 


                // Yeni eklenmesi gereken veriler
                $group_insertValues[] = sprintf(
                    "('%s', %d, %d, NOW())",
                    $geckoid,
                    $metric_group_id,
                    $group_score
                );
            }




            /*
            

            if ($is_it_update) {

                $call = mysqli_prepare($link, 'update coin_group_scores
        set score=round(?), update_date=now() where geckoid=? and metric_group=?');
                mysqli_stmt_bind_param(
                    $call,
                    'isi',
                    $group_score,
                    $geckoid,
                    $metric_group_id
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id2=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $call = mysqli_prepare($link, 'insert into coin_group_scores
        (geckoid, metric_group, score  ) 
        values (?, ?, round(?))');
                mysqli_stmt_bind_param(
                    $call,
                    'sii',
                    $geckoid,
                    $metric_group_id,
                    $group_score
                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id3=" . $geckoid, 0);
                    error_log(mysqli_error($link), 0);
                }
            }

            */


        }

        bulk_update_db("ico_group_scores",$group_updateCases,$group_updateIds,$group_insertValues);




        $call = mysqli_prepare($link, 'update cr_publicsales
    set total_score=round(?) where cr_key=? ');
        mysqli_stmt_bind_param(
            $call,
            'is',
            $total_score,
            $geckoid
        );
        $rs = (mysqli_stmt_execute($call));

        if (!$rs) {
            $success = false;
            error_log("gecko_id4=" . $geckoid, 0);
            error_log(mysqli_error($link), 0);
        }




        //  if($k == 10)
        //  break;

        if (($k + 1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $k /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


        if (($k > 0 && $k % 300 == 0 ) || $k == ($coin_count - 1) ) {

            bulk_update_db("ico_scores", $updateCases, $updateIds, $insertValues);

            $updateCases = [];
            $updateIds = [];
            $insertValues = [];
            



        }
    }



    //score history tablosunu dolduralım.

    $call = mysqli_prepare($link, "
    insert into ico_total_score_history (cr_id,cr_key,score,score_date) 
    select cr_id, cr_key, (select ifnull(total_score ,0)) as score,
     update_date as score_date from cr_publicsales
    ");
    (mysqli_stmt_execute($call));

    









    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }


    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='ico_scores'");
    (mysqli_stmt_execute($call));

    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function bulk_update_db($update_type, $updateCases, $updateIds, $insertValues)
{

    global $link;


    if ($update_type == "scores") {

        // Toplu UPDATE
        if (!empty($updateCases)) {
            $updateQuery = "
        UPDATE coin_scores
        SET score = CASE " . implode(' ', $updateCases) . "
        END,
        update_date = NOW()
        WHERE (geckoid, metric_subgroup) IN (" . implode(',', $updateIds) . ");
    ";
            $rs = mysqli_query($link, $updateQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }

        // Toplu INSERT
        if (!empty($insertValues)) {
            $insertQuery = "
        INSERT INTO coin_scores (geckoid, metric_subgroup, score, update_date)
        VALUES " . implode(',', $insertValues) . ";
    ";
            $rs = mysqli_query($link, $insertQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }
    }

     if ($update_type == "specials") {

        // Toplu UPDATE
        if (!empty($updateCases)) {
            $updateQuery = "
        UPDATE metric_specials
        SET metric_value = CASE " . implode(' ', $updateCases) . "
        END,
        updated_at = NOW()
        WHERE (coin_id, metric_id, metric_key) IN (" . implode(',', $updateIds) . ");
    ";
            $rs = mysqli_query($link, $updateQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }

        // Toplu INSERT
        if (!empty($insertValues)) {
            $insertQuery = "
        INSERT INTO metric_specials (coin_id, metric_id, metric_key, metric_value, metric_type, updated_at)
        VALUES " . implode(',', $insertValues) . ";
    ";
            $rs = mysqli_query($link, $insertQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }
    }



    if ($update_type == "group_scores") {

        // Toplu UPDATE
        if (!empty($updateCases)) {
            $updateQuery = "
        UPDATE coin_group_scores
        SET score = CASE " . implode(' ', $updateCases) . "
        END,
        update_date = NOW()
        WHERE (geckoid, metric_group) IN (" . implode(',', $updateIds) . ");
    ";
            $rs = mysqli_query($link, $updateQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }

        // Toplu INSERT
        if (!empty($insertValues)) {
            $insertQuery = "
        INSERT INTO coin_group_scores (geckoid, metric_group, score, update_date)
        VALUES " . implode(',', $insertValues) . ";
    ";
            $rs = mysqli_query($link, $insertQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }
    }


    if ($update_type == "ico_scores") {

        // Toplu UPDATE
        if (!empty($updateCases)) {
            $updateQuery = "
        UPDATE ico_coin_scores
        SET score = CASE " . implode(' ', $updateCases) . "
        END,
        update_date = NOW()
        WHERE (geckoid, metric_subgroup) IN (" . implode(',', $updateIds) . ");
    ";
            $rs = mysqli_query($link, $updateQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }

        // Toplu INSERT
        if (!empty($insertValues)) {
            $insertQuery = "
        INSERT INTO ico_coin_scores (geckoid, metric_subgroup, score, update_date)
        VALUES " . implode(',', $insertValues) . ";
    ";
            $rs = mysqli_query($link, $insertQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }
    }


    if ($update_type == "ico_group_scores") {

        // Toplu UPDATE
        if (!empty($updateCases)) {
            $updateQuery = "
        UPDATE ico_coin_group_scores
        SET score = CASE " . implode(' ', $updateCases) . "
        END,
        update_date = NOW()
        WHERE (geckoid, metric_group) IN (" . implode(',', $updateIds) . ");
    ";
            $rs = mysqli_query($link, $updateQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }

        // Toplu INSERT
        if (!empty($insertValues)) {
            $insertQuery = "
        INSERT INTO ico_coin_group_scores (geckoid, metric_group, score, update_date)
        VALUES " . implode(',', $insertValues) . ";
    ";
            $rs = mysqli_query($link, $insertQuery);
            if (!$rs) {
                error_log(mysqli_error($link), 0);
            }
        }
    }



    
}


function get_certik_security_scores()
{
    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $certik_api_key, $is_debug;


    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='certik_scores'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'certik_scores', now() )");
    (mysqli_stmt_execute($call));




    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT certiKProjectId from certik_security_scores order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }


    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));





    $curl = curl_init();

    $skip = 0;
    $limit = 1000;



    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;


    $success = true;

    for ($k = 0; $k < 20; $k++) {


        $skip = $k * $limit;



        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://partner.certik-skynet.com/v1/security-scores?skip=' . $skip . '&limit=' . $limit,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'X-Certik-Api-Key: ' . $certik_api_key,
            ),
        ));



        $response = curl_exec($curl);

        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        if ($httpcode != 200) {

            error_log("Response = " . $response, 0);
            continue;
        }



        $crtk_resp = json_decode($response, true);
        $crtk_coins = $crtk_resp['items'];


        for ($i = 0; $i < sizeof($crtk_coins); $i++) {

            $progressed_coin++;


            if (
                $crtk_coins[$i]['certiKProjectId'] == 'bullish-trump-coin' ||
                $crtk_coins[$i]['certiKProjectId'] == 'harrypottertrumpsonic100inu'
            )
                continue;


            $certik_project_id =  $crtk_coins[$i]['certiKProjectId'];
            $certiKProjectPage =  $crtk_coins[$i]['certiKProjectPage'];
            $projectTokenStatus =  $crtk_coins[$i]['projectTokenStatus'];
            $primaryTokenContractAddress =  $crtk_coins[$i]['primaryTokenContractAddress'];
            $projectWebsite =  $crtk_coins[$i]['projectWebsite'];

            if (sizeof($crtk_coins[$i]['tokenTickers']) > 0)
                $tokenTickers =  $crtk_coins[$i]['tokenTickers'][0];


            $auditedByCertiK =  $crtk_coins[$i]['auditedByCertiK'];
            if ($auditedByCertiK) {
                $auditedByCertiK = 1;
      
                if (is_numeric($crtk_coins[$i]['lastAuditDate'])) {
                $lastAuditDate =  date('Y-m-d H:i:s', intdiv($crtk_coins[$i]['lastAuditDate'],1000)); 
                }
                else
                {
                    $lastAuditDate =   date('Y-m-d H:i:s', strtotime($crtk_coins[$i]['lastAuditDate']));
                }
                
            } else {
                $auditedByCertiK = 0;
                $lastAuditDate = '1970-01-01 00:00';
            }



            $score =  $crtk_coins[$i]['securityScore']['score'];
            $rank =   $crtk_coins[$i]['securityScore']['rank'];
            $tier =   $crtk_coins[$i]['securityScore']['tier'];
            $rankPercentile =   $crtk_coins[$i]['securityScore']['rankPercentile'];
            $codeSecurity =   $crtk_coins[$i]['securityScore']['codeSecurity'];
            $communityTrust =   $crtk_coins[$i]['securityScore']['communityTrust'];
            $fundamentalHealth =   $crtk_coins[$i]['securityScore']['fundamentalHealth'];
            $governanceStrength =   $crtk_coins[$i]['securityScore']['governanceStrength'];
            $marketStability =   $crtk_coins[$i]['securityScore']['marketStability'];
            $operationalResilience =   $crtk_coins[$i]['securityScore']['operationalResilience'];
            $coinGeckoSlug =  $crtk_coins[$i]['coinGeckoSlug'];
            $cmcSlug =  $crtk_coins[$i]['cmcSlug'];



            $certiKProjectPage = isset($certiKProjectPage) ? $certiKProjectPage : NULL;
            $projectTokenStatus = isset($projectTokenStatus) ? $projectTokenStatus : NULL;
            $primaryTokenContractAddress = isset($primaryTokenContractAddress) ? $primaryTokenContractAddress : NULL;
            $projectWebsite = isset($projectWebsite) ? $projectWebsite : NULL;
            $tokenTickers = isset($tokenTickers) ? $tokenTickers : NULL;
            $auditedByCertiK = isset($auditedByCertiK) ? $auditedByCertiK : NULL;
            $lastAuditDate = isset($lastAuditDate) ? $lastAuditDate : NULL;
            $score = isset($score) ? $score : NULL;
            $rank = isset($rank) ? $rank : NULL;
            $tier = isset($tier) ? $tier : NULL;
            $rankPercentile = isset($rankPercentile) ? $rankPercentile : NULL;
            $codeSecurity = isset($codeSecurity) ? $codeSecurity : NULL;
            $communityTrust = isset($communityTrust) ? $communityTrust : NULL;
            $fundamentalHealth = isset($fundamentalHealth) ? $fundamentalHealth : NULL;
            $governanceStrength = isset($governanceStrength) ? $governanceStrength : NULL;
            $marketStability = isset($marketStability) ? $marketStability : NULL;
            $operationalResilience = isset($operationalResilience) ? $operationalResilience : NULL;



            /*
            error_log($certik_project_id, 0);
            error_log($certiKProjectPage, 0);
            error_log($projectTokenStatus, 0);
            error_log($primaryTokenContractAddress, 0);
            error_log($projectWebsite, 0);
            error_log($tokenTickers, 0);
            error_log($auditedByCertiK, 0);
            error_log($lastAuditDate, 0);
            error_log($score, 0);
            error_log($rank, 0);
            error_log($tier, 0);
            error_log($rankPercentile, 0);
            error_log($codeSecurity, 0);
            error_log($communityTrust, 0);
            error_log($fundamentalHealth, 0);
            error_log($governanceStrength, 0);
            error_log($marketStability, 0);
            error_log($operationalResilience, 0);

            return;
            */



            $is_existing_coin = false;
            for ($s = 0; $s < sizeof($our_data); $s++) {
                if (strcmp($our_data[$s]["certiKProjectId"], $certik_project_id) == 0) {
                    $is_existing_coin = true;
                    break;
                }
            }


            if ($is_existing_coin) {

                $changed_count++;

                $call = mysqli_prepare($link, 'update certik_security_scores 
          set  certiKProjectPage=?, projectTokenStatus=?, primaryTokenContractAddress=?,
          projectWebsite=?, tokenTickers=?, auditedByCertiK=?, lastAuditDate=?, score=?, ranking=?, tier=?,
          rankPercentile=?, codeSecurity=?, communityTrust=?, fundamentalHealth=?, governanceStrength=?, 
          marketStability=?, operationalResilience=?, cmcSlug=?, coinGeckoSlug=?,
          update_date=now() where certiKProjectId = ?');

                mysqli_stmt_bind_param(
                    $call,
                    'sssssisdisdddddddsss',
                    $certiKProjectPage,
                    $projectTokenStatus,
                    $primaryTokenContractAddress,
                    $projectWebsite,
                    $tokenTickers,
                    $auditedByCertiK,
                    $lastAuditDate,
                    $score,
                    $rank,
                    $tier,
                    $rankPercentile,
                    $codeSecurity,
                    $communityTrust,
                    $fundamentalHealth,
                    $governanceStrength,
                    $marketStability,
                    $operationalResilience,
                    $cmcSlug,
                    $coinGeckoSlug,
                    $certik_project_id

                );
                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $certik_project_id, 0);
                    error_log(mysqli_error($link), 0);
                }
            } else {

                $new_count++;

                $call = mysqli_prepare($link, 'insert into certik_security_scores 
          (certiKProjectId, certiKProjectPage, projectTokenStatus, primaryTokenContractAddress,
           projectWebsite, tokenTickers, auditedByCertiK, lastAuditDate, score, ranking, tier,
            rankPercentile, codeSecurity, communityTrust, fundamentalHealth, governanceStrength, 
           marketStability, operationalResilience, cmcSlug, coinGeckoSlug, create_date, update_date ) 
           values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now() )');

                if (!$call) {
                    error_log(mysqli_error($link), 0);
                }

                mysqli_stmt_bind_param(
                    $call,
                    'ssssssisdisdddddddss',
                    $certik_project_id,
                    $certiKProjectPage,
                    $projectTokenStatus,
                    $primaryTokenContractAddress,
                    $projectWebsite,
                    $tokenTickers,
                    $auditedByCertiK,
                    $lastAuditDate,
                    $score,
                    $rank,
                    $tier,
                    $rankPercentile,
                    $codeSecurity,
                    $communityTrust,
                    $fundamentalHealth,
                    $governanceStrength,
                    $marketStability,
                    $operationalResilience,
                    $cmcSlug,
                    $coinGeckoSlug,
                );

                $rs = (mysqli_stmt_execute($call));

                if (!$rs) {
                    $success = false;
                    error_log("gecko_id=" . $certik_project_id, 0);
                    error_log(mysqli_error($link), 0);
                }
            }


            if (  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
                $progress = $progressed_coin /  (sizeof($our_data) / 100);
    
                $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
                (mysqli_stmt_execute($call));
            }


        }

        $is_finished = $crtk_resp['page']['hasMore'];

        if ($is_finished != true) {
            break;
        }
    }







    curl_close($curl);



      
    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='certik_scores'");
    (mysqli_stmt_execute($call));





    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }

    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function get_lunarcrush_scores()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $data, $lunarcrush_api_key, $is_debug;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='lunar_scores'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'lunar_scores', now() )");
    (mysqli_stmt_execute($call));





    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT lunar_id from lunarcrush_social_scores order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }

    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));



    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://lunarcrush.com/api4/public/coins/list/v1',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Authorization: Bearer ' . $lunarcrush_api_key
        ),
    ));

    $response = curl_exec($curl);

    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if ($httpcode != 200) {

        error_log("Response = " . $response, 0);
        return;
    }


    curl_close($curl);

    $lunar_resp = json_decode($response, true);

    $lunar_coins = $lunar_resp['data'];

    $success = true;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;


    for ($i = 0; $i < sizeof($lunar_coins); $i++) {

        $progressed_coin++;

        $lunar_id = $lunar_coins[$i]['id'];
        $symbol = $lunar_coins[$i]['symbol'];
        $name = $lunar_coins[$i]['name'];
        $logo = $lunar_coins[$i]['logo'];
        $interactions_24h = $lunar_coins[$i]['interactions_24h'];
        $social_volume_24h = $lunar_coins[$i]['social_volume_24h'];
        $social_dominance = $lunar_coins[$i]['social_dominance'];
        $market_dominance = $lunar_coins[$i]['market_dominance'];
        $market_dominance_prev = $lunar_coins[$i]['market_dominance_prev'];
        $galaxy_score = $lunar_coins[$i]['galaxy_score'];

        if (isset($lunar_coins[$i]['galaxy_score_previous']))
            $galaxy_score_previous = $lunar_coins[$i]['galaxy_score_previous'];
        else
            $galaxy_score_previous = 0;


        $alt_rank = $lunar_coins[$i]['alt_rank'];
        if (isset($lunar_coins[$i]['alt_rank_previous']))
            $alt_rank_previous = $lunar_coins[$i]['alt_rank_previous'];
        else
            $alt_rank_previous = 0;


        if (isset($lunar_coins[$i]['sentiment']))
            $sentiment = $lunar_coins[$i]['sentiment'];
        else
            $sentiment = 0;

        $topic = $lunar_coins[$i]['topic'];






        $is_existing_coin = false;
        for ($s = 0; $s < sizeof($our_data); $s++) {
            if (strcmp($our_data[$s]["lunar_id"], $lunar_id) == 0) {
                $is_existing_coin = true;
                break;
            }
        }


        if ($is_existing_coin) {

            $changed_count++;


            $call = mysqli_prepare($link, 'UPDATE lunarcrush_social_scores 
        SET 
        name = ?, 
        symbol = ?,
        interactions_24h = ?, 
        social_volume_24h = ?, 
        social_dominance = ?, 
        market_dominance = ?, 
        market_dominance_prev = ?, 
        galaxy_score = ?, 
        galaxy_score_previous = ?, 
        alt_rank = ?, 
        alt_rank_previous = ?, 
        sentiment = ?, 
        topic = ?, 
        logo = ?,
        update_date = now()
    WHERE lunar_id = ?');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "ssiidddiiiiissi", // Veri tipleri
                $name,
                $symbol,
                $interactions_24h,
                $social_volume_24h,
                $social_dominance,
                $market_dominance,
                $market_dominance_prev,
                $galaxy_score,
                $galaxy_score_previous,
                $alt_rank,
                $alt_rank_previous,
                $sentiment,
                $topic,
                $logo,
                $lunar_id

            );


            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("gecko_id=" . $symbol, 0);
                error_log(mysqli_error($link), 0);
            }
        } else {

            $new_count++;


            $call = mysqli_prepare($link, 'INSERT INTO lunarcrush_social_scores 
    (lunar_id, symbol, name, interactions_24h, social_volume_24h, social_dominance,
     market_dominance, market_dominance_prev, galaxy_score, galaxy_score_previous,
      alt_rank, alt_rank_previous, sentiment, topic, logo, create_date) 
    VALUES 
    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now() )');

            if (!$call) {
                error_log(mysqli_error($link), 0);
            }

            mysqli_stmt_bind_param(
                $call,
                "issiidddiiiiiss",
                $lunar_id,
                $symbol,
                $name,
                $interactions_24h,
                $social_volume_24h,
                $social_dominance,
                $market_dominance,
                $market_dominance_prev,
                $galaxy_score,
                $galaxy_score_previous,
                $alt_rank,
                $alt_rank_previous,
                $sentiment,
                $topic,
                $logo
            );

            $rs = (mysqli_stmt_execute($call));

            if (!$rs) {
                $success = false;
                error_log("gecko_id=" . $symbol, 0);
                error_log(mysqli_error($link), 0);
            }
        }


        if (  ($progressed_coin+1)  %  (sizeof($our_data) / 100) == 0) {
            $progress = $progressed_coin /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }


    }


    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='lunar_scores'");
    (mysqli_stmt_execute($call));





    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function certik_matcher()
{

    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $data, $link, $cr_api_key;


    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT coindata.*, coindata4.web_slug from coindata
                                inner join coindata4 on coindata.geckoslug = coindata4.geckoid");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }




    $certik_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT * from certik_security_scores order by id asc");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $certik_data[] = $obj2;
    }



    for ($i = 0; $i < sizeof($our_data); $i++) {
        for ($k = 0; $k < sizeof($certik_data); $k++) {


            if (($our_data[$i]['geckoslug'] != NULL && $our_data[$i]['geckoslug'] == $certik_data[$k]['coinGeckoSlug']) ||
                ($our_data[$i]['geckoslug'] != NULL && $our_data[$i]['geckoslug'] == $certik_data[$k]['cmcslug'])  ||
                ($our_data[$i]['web_slug'] != NULL && $our_data[$i]['web_slug'] == $certik_data[$k]['coinGeckoSlug'])  ||
                ($our_data[$i]['web_slug'] != NULL && $our_data[$i]['web_slug'] == $certik_data[$k]['cmcslug']) ||
                ($our_data[$i]['geckoid'] != NULL && $our_data[$i]['geckoid'] == $certik_data[$k]['coinGeckoSlug'])  ||
                ($our_data[$i]['geckoid'] != NULL && $our_data[$i]['geckoid'] == $certik_data[$k]['cmcslug'])
            ) {
                $call = mysqli_prepare($link, 'update coin_matcher set certik_project_id=?  where gecko_slug = ?');
                mysqli_stmt_bind_param($call, 'ss', $certik_data[$k]['certiKProjectId'], $our_data[$i]['geckoslug']);
                $rs = (mysqli_stmt_execute($call));
                break;
            }
        }
    }


    //now try to match other nulls with symbols

    $unmatched_certik_coins = array();
    $obj2 = array();
    $rs = mysqli_query($link, "select * from cr_gecko_matcher where cr_id in (select cr_id from coin_matcher where certik_project_id is null)");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $unmatched_certik_coins[] = $obj2;
    }

    for ($i = 0; $i < sizeof($unmatched_certik_coins); $i++) {

        for ($k = 0; $k < sizeof($certik_data); $k++) {

            if ($unmatched_certik_coins[$i]['symbol'] == $certik_data[$k]['tokenTickers']) {

                $call = mysqli_prepare($link, 'update coin_matcher set certik_project_id=?, helper=3  where symbol = ?');
                mysqli_stmt_bind_param($call, 'ss', $certik_data[$k]['certiKProjectId'], $unmatched_certik_coins[$i]['symbol']);
                $rs = (mysqli_stmt_execute($call));
                break;
            }
        }
    }
}



function get_sparkline_history()
{


    ini_set('memory_limit', '-1');
    set_time_limit(0);

    global $link, $cr_api_key, $is_debug;



    $call = mysqli_prepare($link, "update sync_status set isrunning='true', update_date=now() where sync_type='sparkline_history'");
    (mysqli_stmt_execute($call));



    $sync_id = SYNC_GUID2();
    $call = mysqli_prepare($link, "insert into sync_history (sync_id, sync_type, start_date) values ('$sync_id', 'sparkline_history', now() )");
    (mysqli_stmt_execute($call));





    //coindata includes all coin general data,
    //so we are going to ask detailed coin data for each of that table items
    $our_data = array();
    $obj2 = array();
    $rs = mysqli_query($link, "SELECT cr_id, geckoid from coindata");
    while ($obj2 = mysqli_fetch_assoc($rs)) {
        $our_data[] = $obj2;
    }

    $our_data_count = sizeof($our_data);

    $call = mysqli_prepare($link, "update sync_history set total_product=" . $our_data_count . " where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));


    $progressed_coin = 0;

    for($i=0; $i<$our_data_count; $i++) {

        $progressed_coin++;
        $coin_id = $our_data[$i]['cr_id'];
        $coin_key = $our_data[$i]['geckoid'];


        $seven_days_ago = strtotime('-30 days'); // 7 gün önceki zaman (saniye cinsinden)
        $from_timestamp = $seven_days_ago * 1000; // Milisaniyeye çeviriyoruz


   $curl = curl_init();
   $spark_interval = "1d";

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://api.cryptorank.io/v2/currencies/'.$coin_id.'/sparkline?from='.$from_timestamp.'&interval='.$spark_interval.'&sortBy=date&sortDirection=ASC&limit=100&skip=0',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'X-Api-Key: ' . $cr_api_key,
        ),
    ));

    $response = curl_exec($curl);

    $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    if ($httpcode != 200) {

        error_log("Response = " . $response, 0);
        return;
    }


    curl_close($curl);

    $sparkline_resp = json_decode($response, true);

    $sparkline_data = $sparkline_resp['data'];

    $spark_values = $sparkline_data['values'];

    $success = true;

    $progressed_coin = 0;
    $changed_count = 0;
    $new_count = 0;



    for ($z = 0; $z < sizeof($spark_values); $z++) {

        $progressed_coin++;

       // error_log(json_encode($sparkline_data),0);
        $cr_key = $sparkline_data['id'];
     

     


            $timestamp = date('Y-m-d H:i:s', $spark_values[$z]['timestamp'] / 1000); // Timestamp'i datetime formatına çeviriyoruz
            $value = $spark_values[$z]['price']; // Fiyat bilgisini alıyoruz

            // SQL sorgusunu hazırlıyoruz
            $query = "INSERT INTO cr_sparkline (cr_key, spark_timestamp, spark_interval, spark_value)
                    VALUES (?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    spark_value = VALUES(spark_value), update_date = CURRENT_TIMESTAMP";

            // Prepared statement kullanıyoruz
            $stmt = mysqli_prepare($link, $query);

            // Parametreleri bağlama
            mysqli_stmt_bind_param($stmt, "issd", $coin_id, $timestamp, $spark_interval, $value);

            // Sorguyu çalıştırıyoruz
            if (!mysqli_stmt_execute($stmt)) {
                // Eğer sorgu başarısız olursa hata mesajı ver
                echo "Error: " . mysqli_error($link);
            }

            // Prepared statement'i kapatıyoruz
            mysqli_stmt_close($stmt);



    


    }

           $step = $our_data_count > 0 ? max(1, intval($our_data_count / 100)) : 1;

        if (  ($progressed_coin+1)  %  $step == 0) {
            $progress = $progressed_coin /  (sizeof($our_data) / 100);

            $call = mysqli_prepare($link, "update sync_history set progress_percent=" . $progress . " where sync_id='" . $sync_id . "'");
            (mysqli_stmt_execute($call));
        }



         usleep(700000);

    }


    //If No Error Occured Then Commit Changes To Db
    if ($success) {
        $resp = array("status" => "success");
    } else {
        $resp = array("status" => "fail", "details" => mysqli_error($link));
        error_log("Error details: " . mysqli_error($link), 0);
    }



    $call = mysqli_prepare($link, "update sync_history set end_date=now(), changed_product=" . $changed_count . ", new_count=" . $new_count . ", progress_percent=100, completed=1 where sync_id='" . $sync_id . "'");
    (mysqli_stmt_execute($call));

    $call = mysqli_prepare($link, "update sync_status set isrunning='false', update_date=now() where sync_type='sparkline_history'");
    (mysqli_stmt_execute($call));





    echo json_encode($resp, JSON_PRETTY_PRINT);
}


function calculateEmissionRatio(array $allocations, float $maxSupply, int $startDateMillis, int $endDateMillis): float {
    if ($maxSupply <= 0) {
        return 0.0; // Bölme hatası olmasın diye
    }

    $totalEmission = 0.0;

    foreach ($allocations as $allocation) {

       $unlockType = $allocation['unlockType'] ?? null;
        $tgeUnlock = floatval($allocation['tgeUnlock'] ?? 0);

            if (!$unlockType) {
                // unlockType yoksa atla veya farklı işlem yap
                continue;
            }

      
        $totalTokens = floatval($allocation['tokens']);

        if ($unlockType === 'linear') {
            $vestingStart = intval($allocation['vestingStartDate']);
            $vestingEnd = intval($allocation['vestingEndDate']);

            $unlockableTokens = $totalTokens - $tgeUnlock;
            $totalVestingDays = ($vestingEnd - $vestingStart) / (1000 * 60 * 60 * 24);

            if ($totalVestingDays <= 0) continue;

            $dailyUnlock = $unlockableTokens / $totalVestingDays;

            $rangeStart = max($vestingStart, $startDateMillis);
            $rangeEnd = min($vestingEnd, $endDateMillis);

            if ($rangeEnd > $rangeStart) {
                $daysInRange = ($rangeEnd - $rangeStart) / (1000 * 60 * 60 * 24);
                $emission = $dailyUnlock * $daysInRange;
                $totalEmission += $emission;
            }
        } elseif ($unlockType === 'nonlinear') {
            if (isset($allocation['vestingSchedule'])) {
                foreach ($allocation['vestingSchedule'] as $unlock) {
                    $unlockDate = intval($unlock['date']);
                    $tokens = floatval($unlock['tokens']);

                    if ($unlockDate >= $startDateMillis && $unlockDate < $endDateMillis) {
                        $totalEmission += $tokens;
                    }
                }
            }
        }
    }

   $emissionRatio = $totalEmission / $maxSupply;

    $percentage = $emissionRatio * 100;

    return round($percentage, 2);
}

function get_ai_special_data($cr_id, $ai_metric_data, $metric_id)
{

            global $link;

                $uniq_key = $cr_id.'_'.$metric_id;

                if(!isset( $ai_metric_data[$uniq_key]))
                return  $metric_special_value = "";

                $ai_metric_detail = $ai_metric_data[$uniq_key] ;
                $metric_type = 1;
                $metric_special_key = "token_use_case";
                if(isset($ai_metric_detail))
                {
                $metric_special_value = json_encode($ai_metric_detail);
                }
                else
                {
                $metric_special_value = "";
                }

                $metric_special_value = mysqli_real_escape_string($link, $metric_special_value);
                return $metric_special_value;


}







//---------------------------------------------------------------------//
//Utility Functions                                                    //
//---------------------------------------------------------------------//
function cors()
{
    // Allow from any origin
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }

    // Access-Control headers are received during OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
            // may also be using PUT, PATCH, HEAD etc
            header("Access-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS");
        }

        if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
            header("Access-Control-Allow-Headers: Origin, Authorization, X-Requested-With, Content-Type, Accept");
        }

        exit(0);
    }
}



function SYNC_GUID2()
{
    if (function_exists('com_create_guid') === true) {
        return trim(com_create_guid(), '{}');
    }

    return sprintf('%04X%04X-%04X-%04X-%04X-%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(16384, 20479), mt_rand(32768, 49151), mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535));
}
