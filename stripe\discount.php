<?php
/**
 * Stripe Discount Functions
 *
 * This file contains functions related to Stripe discounts and coupons.
 */

// Include required files
require_once __DIR__ . '/StripeLogger.php';

/**
 * Helper function to get user ID from Stripe customer ID
 *
 * @param string $stripeCustomerId The Stripe customer ID
 * @return int|null User ID or null if not found
 */
function getUserIdFromStripeCustomerId($stripeCustomerId) {
    global $link;

    $query = "SELECT id FROM users WHERE stripe_customer_id = ?";
    $stmt = mysqli_prepare($link, $query);

    if (!$stmt) {
        StripeLogger::log(StripeLogLevel::ERROR, "Database error in getUserIdFromStripeCustomerId: " . mysqli_error($link));
        return null;
    }

    mysqli_stmt_bind_param($stmt, "s", $stripeCustomerId);
    mysqli_stmt_execute($stmt);
    mysqli_stmt_bind_result($stmt, $userId);

    if (mysqli_stmt_fetch($stmt)) {
        mysqli_stmt_close($stmt);
        return $userId;
    }

    mysqli_stmt_close($stmt);
    return null;
}

/**
 * Handle customer discount created event
 *
 * @param object $discount The discount object from Stripe
 */
function handleCustomerDiscountCreated($discount) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER DISCOUNT CREATED - Customer: {$discount->customer}, Coupon: {$discount->coupon->id}");

    try {
        // Get user ID from customer ID
        $userId = getUserIdFromStripeCustomerId($discount->customer);

        if (!$userId) {
            StripeLogger::log(StripeLogLevel::ERROR, "STRIPE DISCOUNT ERROR - User not found for customer ID: {$discount->customer}");
            return;
        }

        // Check if discount already exists
        $checkQuery = "SELECT id FROM stripe_customer_discounts
                      WHERE stripe_customer_id = ? AND stripe_coupon_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "ss", $discount->customer, $discount->coupon->id);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_store_result($checkStmt);

        if (mysqli_stmt_num_rows($checkStmt) > 0) {
            // Discount already exists, update it
            handleCustomerDiscountUpdated($discount);
            return;
        }

        // Insert new discount
        $insertQuery = "INSERT INTO stripe_customer_discounts (
            user_id,
            stripe_customer_id,
            stripe_subscription_id,
            stripe_coupon_id,
            percent_off,
            amount_off,
            currency,
            start_date,
            end_date,
            is_active
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $subscriptionId = isset($discount->subscription) ? $discount->subscription : null;
        $percentOff = isset($discount->coupon->percent_off) ? $discount->coupon->percent_off : null;
        $amountOff = isset($discount->coupon->amount_off) ? $discount->coupon->amount_off / 100 : null; // Convert from cents
        $currency = isset($discount->coupon->currency) ? $discount->coupon->currency : null;
        $startDate = date('Y-m-d H:i:s', $discount->start);
        $endDate = isset($discount->end) ? date('Y-m-d H:i:s', $discount->end) : null;

        mysqli_stmt_bind_param(
            $insertStmt,
            "isssdsss",
            $userId,
            $discount->customer,
            $subscriptionId,
            $discount->coupon->id,
            $percentOff,
            $amountOff,
            $currency,
            $startDate,
            $endDate
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to insert discount: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER DISCOUNT INSERTED INTO DB - Customer: {$discount->customer}");

        // Add notification for user
        $notificationQuery = "INSERT INTO user_notifications
                             (user_id, type, title, message, is_read, created_at)
                             VALUES (?, 'discount_applied', 'Discount Applied',
                             'A discount has been applied to your subscription.', 0, NOW())";

        $notificationStmt = mysqli_prepare($link, $notificationQuery);

        if ($notificationStmt) {
            mysqli_stmt_bind_param($notificationStmt, "i", $userId);
            mysqli_stmt_execute($notificationStmt);
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE DISCOUNT CREATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'customer' => $discount->customer ?? 'unknown',
            'couponId' => $discount->coupon->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle customer discount updated event
 *
 * @param object $discount The discount object from Stripe
 */
function handleCustomerDiscountUpdated($discount) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER DISCOUNT UPDATED - Customer: {$discount->customer}, Coupon: {$discount->coupon->id}");

    try {
        // Get user ID from customer ID
        $userId = getUserIdFromStripeCustomerId($discount->customer);

        if (!$userId) {
            StripeLogger::log(StripeLogLevel::ERROR, "STRIPE DISCOUNT ERROR - User not found for customer ID: {$discount->customer}");
            return;
        }

        // Update discount in database
        $updateQuery = "UPDATE stripe_customer_discounts SET
            stripe_subscription_id = ?,
            percent_off = ?,
            amount_off = ?,
            currency = ?,
            start_date = ?,
            end_date = ?,
            is_active = 1,
            updated_at = NOW()
            WHERE stripe_customer_id = ? AND stripe_coupon_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $subscriptionId = isset($discount->subscription) ? $discount->subscription : null;
        $percentOff = isset($discount->coupon->percent_off) ? $discount->coupon->percent_off : null;
        $amountOff = isset($discount->coupon->amount_off) ? $discount->coupon->amount_off / 100 : null; // Convert from cents
        $currency = isset($discount->coupon->currency) ? $discount->coupon->currency : null;
        $startDate = date('Y-m-d H:i:s', $discount->start);
        $endDate = isset($discount->end) ? date('Y-m-d H:i:s', $discount->end) : null;

        mysqli_stmt_bind_param(
            $updateStmt,
            "sddsssss",
            $subscriptionId,
            $percentOff,
            $amountOff,
            $currency,
            $startDate,
            $endDate,
            $discount->customer,
            $discount->coupon->id
        );

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update discount: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER DISCOUNT UPDATED IN DB - Customer: {$discount->customer}");
        } else {
            // Discount doesn't exist, create it
            handleCustomerDiscountCreated($discount);
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE DISCOUNT UPDATE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'customer' => $discount->customer ?? 'unknown',
            'couponId' => $discount->coupon->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle customer discount deleted event
 *
 * @param object $discount The discount object from Stripe
 */
function handleCustomerDiscountDeleted($discount) {
    global $link;

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE CUSTOMER DISCOUNT DELETED - Customer: {$discount->customer}, Coupon: {$discount->coupon->id}");

    try {
        // Get user ID from customer ID
        $userId = getUserIdFromStripeCustomerId($discount->customer);

        if (!$userId) {
            StripeLogger::log(StripeLogLevel::ERROR, "STRIPE DISCOUNT ERROR - User not found for customer ID: {$discount->customer}");
            return;
        }

        // Mark discount as inactive in database
        $updateQuery = "UPDATE stripe_customer_discounts SET
            is_active = 0,
            updated_at = NOW()
            WHERE stripe_customer_id = ? AND stripe_coupon_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($updateStmt, "ss", $discount->customer, $discount->coupon->id);

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update discount: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE CUSTOMER DISCOUNT MARKED INACTIVE IN DB - Customer: {$discount->customer}");

        // Add notification for user
        $notificationQuery = "INSERT INTO user_notifications
                             (user_id, type, title, message, is_read, created_at)
                             VALUES (?, 'discount_removed', 'Discount Removed',
                             'A discount has been removed from your subscription.', 0, NOW())";

        $notificationStmt = mysqli_prepare($link, $notificationQuery);

        if ($notificationStmt) {
            mysqli_stmt_bind_param($notificationStmt, "i", $userId);
            mysqli_stmt_execute($notificationStmt);
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE DISCOUNT DELETION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'customer' => $discount->customer ?? 'unknown',
            'couponId' => $discount->coupon->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle coupon created event
 *
 * @param object $coupon The coupon object from Stripe
 */
function handleCouponCreated($coupon) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE COUPON CREATED - ID: {$coupon->id}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($coupon->metadata);

        // Check if coupon already exists
        $checkQuery = "SELECT id FROM stripe_coupons WHERE stripe_coupon_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "s", $coupon->id);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_store_result($checkStmt);

        if (mysqli_stmt_num_rows($checkStmt) > 0) {
            // Coupon already exists, update it
            handleCouponUpdated($coupon);
            return;
        }

        // Insert new coupon
        $insertQuery = "INSERT INTO stripe_coupons (
            stripe_coupon_id,
            name,
            percent_off,
            amount_off,
            currency,
            duration,
            duration_in_months,
            max_redemptions,
            times_redeemed,
            valid_until,
            is_active,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $name = isset($coupon->name) ? $coupon->name : null;
        $percentOff = isset($coupon->percent_off) ? $coupon->percent_off : null;
        $amountOff = isset($coupon->amount_off) ? $coupon->amount_off / 100 : null; // Convert from cents
        $currency = isset($coupon->currency) ? $coupon->currency : null;
        $duration = $coupon->duration;
        $durationInMonths = isset($coupon->duration_in_months) ? $coupon->duration_in_months : null;
        $maxRedemptions = isset($coupon->max_redemptions) ? $coupon->max_redemptions : null;
        $timesRedeemed = isset($coupon->times_redeemed) ? $coupon->times_redeemed : 0;
        $validUntil = isset($coupon->redeem_by) ? date('Y-m-d H:i:s', $coupon->redeem_by) : null;
        $isActive = $coupon->valid ? 1 : 0;

        mysqli_stmt_bind_param(
            $insertStmt,
            "ssdsssiiisis",
            $coupon->id,
            $name,
            $percentOff,
            $amountOff,
            $currency,
            $duration,
            $durationInMonths,
            $maxRedemptions,
            $timesRedeemed,
            $validUntil,
            $isActive,
            $metadata
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to insert coupon: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE COUPON INSERTED INTO DB - ID: {$coupon->id}");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE COUPON CREATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'couponId' => $coupon->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle coupon updated event
 *
 * @param object $coupon The coupon object from Stripe
 */
function handleCouponUpdated($coupon) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE COUPON UPDATED - ID: {$coupon->id}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($coupon->metadata);

        // Update coupon in database
        $updateQuery = "UPDATE stripe_coupons SET
            name = ?,
            percent_off = ?,
            amount_off = ?,
            currency = ?,
            duration = ?,
            duration_in_months = ?,
            max_redemptions = ?,
            times_redeemed = ?,
            valid_until = ?,
            is_active = ?,
            metadata = ?,
            updated_at = NOW()
            WHERE stripe_coupon_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $name = isset($coupon->name) ? $coupon->name : null;
        $percentOff = isset($coupon->percent_off) ? $coupon->percent_off : null;
        $amountOff = isset($coupon->amount_off) ? $coupon->amount_off / 100 : null; // Convert from cents
        $currency = isset($coupon->currency) ? $coupon->currency : null;
        $duration = $coupon->duration;
        $durationInMonths = isset($coupon->duration_in_months) ? $coupon->duration_in_months : null;
        $maxRedemptions = isset($coupon->max_redemptions) ? $coupon->max_redemptions : null;
        $timesRedeemed = isset($coupon->times_redeemed) ? $coupon->times_redeemed : 0;
        $validUntil = isset($coupon->redeem_by) ? date('Y-m-d H:i:s', $coupon->redeem_by) : null;
        $isActive = $coupon->valid ? 1 : 0;

        mysqli_stmt_bind_param(
            $updateStmt,
            "sdsssiiisiss",
            $name,
            $percentOff,
            $amountOff,
            $currency,
            $duration,
            $durationInMonths,
            $maxRedemptions,
            $timesRedeemed,
            $validUntil,
            $isActive,
            $metadata,
            $coupon->id
        );

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update coupon: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE COUPON UPDATED IN DB - ID: {$coupon->id}");
        } else {
            // Coupon doesn't exist, create it
            handleCouponCreated($coupon);
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE COUPON UPDATE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'couponId' => $coupon->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle coupon deleted event
 *
 * @param object $coupon The coupon object from Stripe
 */
function handleCouponDeleted($coupon) {
    global $link;

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE COUPON DELETED - ID: {$coupon->id}");

    try {
        // Mark coupon as inactive in database
        $updateQuery = "UPDATE stripe_coupons SET
            is_active = 0,
            updated_at = NOW()
            WHERE stripe_coupon_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($updateStmt, "s", $coupon->id);

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update coupon: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE COUPON MARKED INACTIVE IN DB - ID: {$coupon->id}");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE COUPON DELETION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'couponId' => $coupon->id ?? 'unknown'
        ]);
    }
}

/**
 * Handle promotion code created event
 *
 * @param object $promotionCode The promotion code object from Stripe
 */
function handlePromotionCodeCreated($promotionCode) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PROMOTION CODE CREATED - ID: {$promotionCode->id}, Code: {$promotionCode->code}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($promotionCode->metadata);

        // Check if promotion code already exists
        $checkQuery = "SELECT id FROM stripe_promotion_codes WHERE stripe_promotion_code_id = ?";
        $checkStmt = mysqli_prepare($link, $checkQuery);

        if (!$checkStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($checkStmt, "s", $promotionCode->id);
        mysqli_stmt_execute($checkStmt);
        mysqli_stmt_store_result($checkStmt);

        if (mysqli_stmt_num_rows($checkStmt) > 0) {
            // Promotion code already exists, update it
            handlePromotionCodeUpdated($promotionCode);
            return;
        }

        // Insert new promotion code
        $insertQuery = "INSERT INTO stripe_promotion_codes (
            stripe_promotion_code_id,
            stripe_coupon_id,
            code,
            active,
            expires_at,
            max_redemptions,
            times_redeemed,
            first_time_transaction,
            minimum_amount,
            minimum_amount_currency,
            customer_specific,
            customer_id,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $insertStmt = mysqli_prepare($link, $insertQuery);

        if (!$insertStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $active = $promotionCode->active ? 1 : 0;
        $expiresAt = isset($promotionCode->expires_at) ? date('Y-m-d H:i:s', $promotionCode->expires_at) : null;
        $maxRedemptions = isset($promotionCode->max_redemptions) ? $promotionCode->max_redemptions : null;
        $timesRedeemed = isset($promotionCode->times_redeemed) ? $promotionCode->times_redeemed : 0;
        $firstTimeTransaction = isset($promotionCode->restrictions->first_time_transaction) ?
            ($promotionCode->restrictions->first_time_transaction ? 1 : 0) : 0;
        $minimumAmount = isset($promotionCode->restrictions->minimum_amount) ?
            $promotionCode->restrictions->minimum_amount / 100 : null; // Convert from cents
        $minimumAmountCurrency = isset($promotionCode->restrictions->minimum_amount_currency) ?
            $promotionCode->restrictions->minimum_amount_currency : null;
        $customerSpecific = isset($promotionCode->customer) ? 1 : 0;
        $customerId = isset($promotionCode->customer) ? $promotionCode->customer : null;

        mysqli_stmt_bind_param(
            $insertStmt,
            "sssisiidsisss",
            $promotionCode->id,
            $promotionCode->coupon->id,
            $promotionCode->code,
            $active,
            $expiresAt,
            $maxRedemptions,
            $timesRedeemed,
            $firstTimeTransaction,
            $minimumAmount,
            $minimumAmountCurrency,
            $customerSpecific,
            $customerId,
            $metadata
        );

        if (!mysqli_stmt_execute($insertStmt)) {
            throw new Exception("Failed to insert promotion code: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE PROMOTION CODE INSERTED INTO DB - ID: {$promotionCode->id}, Code: {$promotionCode->code}");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PROMOTION CODE CREATION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'promotionCodeId' => $promotionCode->id ?? 'unknown',
            'code' => $promotionCode->code ?? 'unknown'
        ]);
    }
}

/**
 * Handle promotion code updated event
 *
 * @param object $promotionCode The promotion code object from Stripe
 */
function handlePromotionCodeUpdated($promotionCode) {
    global $link;

    StripeLogger::log(StripeLogLevel::INFO, "STRIPE PROMOTION CODE UPDATED - ID: {$promotionCode->id}, Code: {$promotionCode->code}");

    try {
        // Convert metadata to JSON
        $metadata = json_encode($promotionCode->metadata);

        // Update promotion code in database
        $updateQuery = "UPDATE stripe_promotion_codes SET
            stripe_coupon_id = ?,
            code = ?,
            active = ?,
            expires_at = ?,
            max_redemptions = ?,
            times_redeemed = ?,
            first_time_transaction = ?,
            minimum_amount = ?,
            minimum_amount_currency = ?,
            customer_specific = ?,
            customer_id = ?,
            metadata = ?,
            updated_at = NOW()
            WHERE stripe_promotion_code_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        $active = $promotionCode->active ? 1 : 0;
        $expiresAt = isset($promotionCode->expires_at) ? date('Y-m-d H:i:s', $promotionCode->expires_at) : null;
        $maxRedemptions = isset($promotionCode->max_redemptions) ? $promotionCode->max_redemptions : null;
        $timesRedeemed = isset($promotionCode->times_redeemed) ? $promotionCode->times_redeemed : 0;
        $firstTimeTransaction = isset($promotionCode->restrictions->first_time_transaction) ?
            ($promotionCode->restrictions->first_time_transaction ? 1 : 0) : 0;
        $minimumAmount = isset($promotionCode->restrictions->minimum_amount) ?
            $promotionCode->restrictions->minimum_amount / 100 : null; // Convert from cents
        $minimumAmountCurrency = isset($promotionCode->restrictions->minimum_amount_currency) ?
            $promotionCode->restrictions->minimum_amount_currency : null;
        $customerSpecific = isset($promotionCode->customer) ? 1 : 0;
        $customerId = isset($promotionCode->customer) ? $promotionCode->customer : null;

        mysqli_stmt_bind_param(
            $updateStmt,
            "ssisiidsisss",
            $promotionCode->coupon->id,
            $promotionCode->code,
            $active,
            $expiresAt,
            $maxRedemptions,
            $timesRedeemed,
            $firstTimeTransaction,
            $minimumAmount,
            $minimumAmountCurrency,
            $customerSpecific,
            $customerId,
            $metadata,
            $promotionCode->id
        );

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update promotion code: " . mysqli_error($link));
        }

        $affectedRows = mysqli_affected_rows($link);

        if ($affectedRows > 0) {
            StripeLogger::log(StripeLogLevel::INFO, "STRIPE PROMOTION CODE UPDATED IN DB - ID: {$promotionCode->id}, Code: {$promotionCode->code}");
        } else {
            // Promotion code doesn't exist, create it
            handlePromotionCodeCreated($promotionCode);
        }

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PROMOTION CODE UPDATE ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'promotionCodeId' => $promotionCode->id ?? 'unknown',
            'code' => $promotionCode->code ?? 'unknown'
        ]);
    }
}

/**
 * Handle promotion code deleted event
 *
 * @param object $promotionCode The promotion code object from Stripe
 */
function handlePromotionCodeDeleted($promotionCode) {
    global $link;

    StripeLogger::log(StripeLogLevel::WARNING, "STRIPE PROMOTION CODE DELETED - ID: {$promotionCode->id}, Code: {$promotionCode->code}");

    try {
        // Mark promotion code as inactive in database
        $updateQuery = "UPDATE stripe_promotion_codes SET
            active = 0,
            updated_at = NOW()
            WHERE stripe_promotion_code_id = ?";

        $updateStmt = mysqli_prepare($link, $updateQuery);

        if (!$updateStmt) {
            throw new Exception("Database error: " . mysqli_error($link));
        }

        mysqli_stmt_bind_param($updateStmt, "s", $promotionCode->id);

        if (!mysqli_stmt_execute($updateStmt)) {
            throw new Exception("Failed to update promotion code: " . mysqli_error($link));
        }

        StripeLogger::log(StripeLogLevel::INFO, "STRIPE PROMOTION CODE MARKED INACTIVE IN DB - ID: {$promotionCode->id}, Code: {$promotionCode->code}");

    } catch (Exception $e) {
        StripeLogger::log(StripeLogLevel::ERROR, "STRIPE PROMOTION CODE DELETION ERROR: " . $e->getMessage(), [
            'exception' => get_class($e),
            'promotionCodeId' => $promotionCode->id ?? 'unknown',
            'code' => $promotionCode->code ?? 'unknown'
        ]);
    }
}